#!/usr/bin/env python3
"""
测试PLE_V4模型是否能正常运行
"""
import torch
import numpy as np
from ple_v4 import PLE_V4

def test_ple_v4():
    """测试PLE_V4模型的前向传播"""
    print("测试PLE_V4模型...")
    
    # 设置设备
    device = torch.device('cpu')
    
    # 创建模型
    model = PLE_V4(
        item_num=1000,  # 小一点的数值用于测试
        gender_num=3,
        age_num=8,
        item_dim=7,
        n_output=2,
        embedding_dim=64,
        device=device
    ).to(device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建测试数据
    batch_size = 32
    x_item_id = torch.randint(0, 1000, (batch_size,))
    x_gender = torch.randint(0, 3, (batch_size,))
    x_age = torch.randint(0, 8, (batch_size,))
    x_item = torch.randn(batch_size, 7)
    
    print(f"输入形状:")
    print(f"  x_item_id: {x_item_id.shape}")
    print(f"  x_gender: {x_gender.shape}")
    print(f"  x_age: {x_age.shape}")
    print(f"  x_item: {x_item.shape}")
    
    # 前向传播
    try:
        model.eval()
        with torch.no_grad():
            output = model(x_item_id, x_gender, x_age, x_item)
        
        print(f"输出形状: {output.shape}")
        print(f"输出范围: [{output.min():.4f}, {output.max():.4f}]")
        print("✓ 前向传播成功!")
        
        # 测试训练模式
        model.train()
        output_train = model(x_item_id, x_gender, x_age, x_item)
        print(f"训练模式输出形状: {output_train.shape}")
        print("✓ 训练模式成功!")
        
        # 测试梯度计算
        y = torch.randint(0, 2, (batch_size, 2)).float()
        criterion = torch.nn.BCELoss()
        loss = criterion(output_train, y)
        loss.backward()
        print(f"损失值: {loss.item():.4f}")
        print("✓ 梯度计算成功!")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_ple_v4()
    if success:
        print("\n🎉 PLE_V4模型测试通过!")
    else:
        print("\n❌ PLE_V4模型测试失败!")
