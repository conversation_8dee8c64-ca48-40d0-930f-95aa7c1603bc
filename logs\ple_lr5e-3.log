nohup: ignoring input
/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/torchmetrics/utilities/prints.py:36: UserWarning: Metric `AUROC` will save all targets and predictions in buffer. For large datasets this may lead to large memory footprint.
  warnings.warn(*args, **kwargs)
Namespace(batch_size=256, checkpoint='checkpoint/ple_best.pth', device=device(type='cpu'), epoches=10, loss=BCELoss(), lr=0.005, model_type='ple', optimizer=Adam (
Parameter Group 0
    amsgrad: False
    betas: (0.9, 0.999)
    eps: 1e-08
    lr: 0.005
    weight_decay: 0
))
Epoch: 0/10, epoch_loss_like: 0.0805, epoch_loss_share: 0.0755    auc_like: 0.792715, auc_share: 0.704965
Test Result: 
Epoch: 0/10, epoch_loss_like: 0.0629, epoch_loss_share: 0.0552    auc_like: 0.861887, auc_share: 0.807359


Epoch: 1/10, epoch_loss_like: 0.0628, epoch_loss_share: 0.0549    auc_like: 0.863344, auc_share: 0.810383
Test Result: 
Epoch: 1/10, epoch_loss_like: 0.0600, epoch_loss_share: 0.0528    auc_like: 0.893588, auc_share: 0.853189


Epoch: 2/10, epoch_loss_like: 0.0593, epoch_loss_share: 0.0521    auc_like: 0.895846, auc_share: 0.855101
Test Result: 
Epoch: 2/10, epoch_loss_like: 0.0560, epoch_loss_share: 0.0497    auc_like: 0.920553, auc_share: 0.892995


Epoch: 3/10, epoch_loss_like: 0.0561, epoch_loss_share: 0.0490    auc_like: 0.917054, auc_share: 0.888541
Test Result: 
Epoch: 3/10, epoch_loss_like: 0.0531, epoch_loss_share: 0.0469    auc_like: 0.936225, auc_share: 0.914605


Epoch: 4/10, epoch_loss_like: 0.0537, epoch_loss_share: 0.0469    auc_like: 0.929400, auc_share: 0.906467
Test Result: 
Epoch: 4/10, epoch_loss_like: 0.0505, epoch_loss_share: 0.0445    auc_like: 0.942928, auc_share: 0.927614


Epoch: 5/10, epoch_loss_like: 0.0519, epoch_loss_share: 0.0450    auc_like: 0.936292, auc_share: 0.918849
Test Result: 
Epoch: 5/10, epoch_loss_like: 0.0501, epoch_loss_share: 0.0429    auc_like: 0.948574, auc_share: 0.934976


Epoch: 6/10, epoch_loss_like: 0.0505, epoch_loss_share: 0.0442    auc_like: 0.940682, auc_share: 0.925048
Test Result: 
Epoch: 6/10, epoch_loss_like: 0.0471, epoch_loss_share: 0.0420    auc_like: 0.952670, auc_share: 0.938772


Epoch: 7/10, epoch_loss_like: 0.0495, epoch_loss_share: 0.0599    auc_like: 0.943574, auc_share: 0.927257
Test Result: 
Epoch: 7/10, epoch_loss_like: 0.0470, epoch_loss_share: 0.0403    auc_like: 0.954810, auc_share: 0.942956


Epoch: 8/10, epoch_loss_like: 0.0484, epoch_loss_share: 0.0431    auc_like: 0.947008, auc_share: 0.933197
Test Result: 
Epoch: 8/10, epoch_loss_like: 0.0457, epoch_loss_share: 0.0400    auc_like: 0.956757, auc_share: 0.945219


Epoch: 9/10, epoch_loss_like: 0.0479, epoch_loss_share: 0.0453    auc_like: 0.947953, auc_share: 0.935684
Test Result: 
Epoch: 9/10, epoch_loss_like: 0.0459, epoch_loss_share: 0.0396    auc_like: 0.957039, auc_share: 0.949593


