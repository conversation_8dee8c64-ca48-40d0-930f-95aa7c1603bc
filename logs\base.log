nohup: ignoring input
/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/torchmetrics/utilities/prints.py:36: UserWarning: Metric `AUROC` will save all targets and predictions in buffer. For large datasets this may lead to large memory footprint.
  warnings.warn(*args, **kwargs)
Namespace(batch_size=256, checkpoint='checkpoint/base_best.pth', device=device(type='cpu'), epoches=10, loss=BCELoss(), lr=0.001, model_type='base', optimizer=Adam (
Parameter Group 0
    amsgrad: False
    betas: (0.9, 0.999)
    eps: 1e-08
    lr: 0.001
    weight_decay: 0
))
Epoch: 0/10, epoch_loss_like: 0.1114, epoch_loss_share: 0.1205    auc_like: 0.719249, auc_share: 0.622840
Test Result: 
Epoch: 0/10, epoch_loss_like: 0.0632, epoch_loss_share: 0.0560    auc_like: 0.859967, auc_share: 0.786907


Epoch: 1/10, epoch_loss_like: 0.0631, epoch_loss_share: 0.0557    auc_like: 0.861153, auc_share: 0.793718
Test Result: 
Epoch: 1/10, epoch_loss_like: 0.0630, epoch_loss_share: 0.0553    auc_like: 0.862625, auc_share: 0.798862


Epoch: 2/10, epoch_loss_like: 0.0630, epoch_loss_share: 0.0551    auc_like: 0.862864, auc_share: 0.804633
Test Result: 
Epoch: 2/10, epoch_loss_like: 0.0628, epoch_loss_share: 0.0543    auc_like: 0.864877, auc_share: 0.818927


Epoch: 3/10, epoch_loss_like: 0.0625, epoch_loss_share: 0.0538    auc_like: 0.868471, auc_share: 0.828594
Test Result: 
Epoch: 3/10, epoch_loss_like: 0.0616, epoch_loss_share: 0.0524    auc_like: 0.876703, auc_share: 0.849802


Epoch: 4/10, epoch_loss_like: 0.0613, epoch_loss_share: 0.0518    auc_like: 0.880707, auc_share: 0.856735
Test Result: 
Epoch: 4/10, epoch_loss_like: 0.0601, epoch_loss_share: 0.0501    auc_like: 0.889996, auc_share: 0.877084


Epoch: 5/10, epoch_loss_like: 0.0599, epoch_loss_share: 0.0499    auc_like: 0.893101, auc_share: 0.879475
Test Result: 
Epoch: 5/10, epoch_loss_like: 0.0585, epoch_loss_share: 0.0483    auc_like: 0.902189, auc_share: 0.895330


Epoch: 6/10, epoch_loss_like: 0.0582, epoch_loss_share: 0.0481    auc_like: 0.904322, auc_share: 0.896101
Test Result: 
Epoch: 6/10, epoch_loss_like: 0.0570, epoch_loss_share: 0.0466    auc_like: 0.913307, auc_share: 0.910399


Epoch: 7/10, epoch_loss_like: 0.0567, epoch_loss_share: 0.0467    auc_like: 0.913295, auc_share: 0.908586
Test Result: 
Epoch: 7/10, epoch_loss_like: 0.0553, epoch_loss_share: 0.0452    auc_like: 0.921333, auc_share: 0.919091


Epoch: 8/10, epoch_loss_like: 0.0555, epoch_loss_share: 0.0454    auc_like: 0.920425, auc_share: 0.917658
Test Result: 
Epoch: 8/10, epoch_loss_like: 0.0545, epoch_loss_share: 0.0438    auc_like: 0.927180, auc_share: 0.927502


Epoch: 9/10, epoch_loss_like: 0.0545, epoch_loss_share: 0.0443    auc_like: 0.925955, auc_share: 0.924977
Test Result: 
Epoch: 9/10, epoch_loss_like: 0.0533, epoch_loss_share: 0.0428    auc_like: 0.931764, auc_share: 0.934108


