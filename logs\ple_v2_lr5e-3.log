nohup: ignoring input
Namespace(batch_size=256, checkpoint='checkpoint/ple_v2_best.pth', device=device(type='cpu'), epoches=10, loss=BCELoss(), lr=0.005, model_type='ple_v2', optimizer=Adam (
Parameter Group 0
    amsgrad: False
    betas: (0.9, 0.999)
    eps: 1e-08
    lr: 0.005
    weight_decay: 0
))
/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/torchmetrics/utilities/prints.py:36: UserWarning: Metric `AUROC` will save all targets and predictions in buffer. For large datasets this may lead to large memory footprint.
  warnings.warn(*args, **kwargs)
Epoch: 0/10, epoch_loss_like: 0.0778, epoch_loss_share: 0.0683    auc_like: 0.814329, auc_share: 0.749559
Test Result: 
Epoch: 0/10, epoch_loss_like: 0.0626, epoch_loss_share: 0.0545    auc_like: 0.868673, auc_share: 0.822362


Epoch: 1/10, epoch_loss_like: 0.0615, epoch_loss_share: 0.0541    auc_like: 0.877142, auc_share: 0.826950
Test Result: 
Epoch: 1/10, epoch_loss_like: 0.0573, epoch_loss_share: 0.0512    auc_like: 0.913251, auc_share: 0.876219


Epoch: 2/10, epoch_loss_like: 0.0565, epoch_loss_share: 0.0499    auc_like: 0.913145, auc_share: 0.881046
Test Result: 
Epoch: 2/10, epoch_loss_like: 0.0514, epoch_loss_share: 0.0470    auc_like: 0.937322, auc_share: 0.911326


Epoch: 3/10, epoch_loss_like: 0.0524, epoch_loss_share: 0.0472    auc_like: 0.933925, auc_share: 0.906746
Test Result: 
Epoch: 3/10, epoch_loss_like: 0.0479, epoch_loss_share: 0.0434    auc_like: 0.954225, auc_share: 0.928807


Epoch: 4/10, epoch_loss_like: 0.0490, epoch_loss_share: 0.0448    auc_like: 0.946456, auc_share: 0.920000
Test Result: 
Epoch: 4/10, epoch_loss_like: 0.0441, epoch_loss_share: 0.0421    auc_like: 0.960872, auc_share: 0.939166


Epoch: 5/10, epoch_loss_like: 0.0463, epoch_loss_share: 0.0432    auc_like: 0.954473, auc_share: 0.929892
Test Result: 
Epoch: 5/10, epoch_loss_like: 0.0418, epoch_loss_share: 0.0400    auc_like: 0.965979, auc_share: 0.945555


Epoch: 6/10, epoch_loss_like: 0.0451, epoch_loss_share: 0.0416    auc_like: 0.958925, auc_share: 0.937565
Test Result: 
Epoch: 6/10, epoch_loss_like: 0.0415, epoch_loss_share: 0.0390    auc_like: 0.967067, auc_share: 0.949238


Epoch: 7/10, epoch_loss_like: 0.0430, epoch_loss_share: 0.0402    auc_like: 0.962536, auc_share: 0.944242
Test Result: 
Epoch: 7/10, epoch_loss_like: 0.0396, epoch_loss_share: 0.0378    auc_like: 0.971857, auc_share: 0.957154


Epoch: 8/10, epoch_loss_like: 0.0420, epoch_loss_share: 0.0390    auc_like: 0.964819, auc_share: 0.949586
Test Result: 
Epoch: 8/10, epoch_loss_like: 0.0387, epoch_loss_share: 0.0367    auc_like: 0.972633, auc_share: 0.959935


Epoch: 9/10, epoch_loss_like: 0.0418, epoch_loss_share: 0.0380    auc_like: 0.965456, auc_share: 0.953204
Test Result: 
Epoch: 9/10, epoch_loss_like: 0.0390, epoch_loss_share: 0.0356    auc_like: 0.974267, auc_share: 0.963796


