#!/usr/bin/env python3
"""
测试AUC修复的脚本
"""
import torch
import numpy as np
from torchmetrics import AUROC

def test_auc_calculation():
    """测试AUC计算是否正确"""
    print("测试AUC计算...")
    
    # 创建一些测试数据
    batch_size = 100
    y_hat = torch.sigmoid(torch.randn(batch_size, 2))  # 预测值，通过sigmoid确保在[0,1]范围
    y = torch.randint(0, 2, (batch_size, 2)).float()  # 真实标签
    
    print(f"预测值范围: [{y_hat.min():.4f}, {y_hat.max():.4f}]")
    print(f"标签值: {torch.unique(y)}")
    
    # 测试原来的方法（可能有问题的）
    try:
        roc_auc_metric = AUROC(task="binary")
        auc_like_old = roc_auc_metric(y_hat[:, 0], y[:, 0].long())
        auc_share_old = roc_auc_metric(y_hat[:, 1], y[:, 1].long())  # 这里可能有问题，因为使用了同一个metric
        print(f"旧方法 - AUC like: {auc_like_old:.6f}, AUC share: {auc_share_old:.6f}")
    except Exception as e:
        print(f"旧方法出错: {e}")
    
    # 测试新的方法（修复后的）
    try:
        roc_auc_like = AUROC(task="binary")
        roc_auc_share = AUROC(task="binary")
        auc_like_new = roc_auc_like(y_hat[:, 0], y[:, 0].long())
        auc_share_new = roc_auc_share(y_hat[:, 1], y[:, 1].long())
        print(f"新方法 - AUC like: {auc_like_new:.6f}, AUC share: {auc_share_new:.6f}")
    except Exception as e:
        print(f"新方法出错: {e}")

def test_early_stopping_logic():
    """测试早停逻辑"""
    print("\n测试早停逻辑...")
    
    # 模拟训练过程中的验证损失
    val_losses = [0.8, 0.7, 0.6, 0.65, 0.64, 0.63, 0.62, 0.61, 0.605, 0.604]
    
    best_loss = float('inf')
    patience_counter = 0
    patience = 5
    
    for epoch, val_loss in enumerate(val_losses):
        print(f"Epoch {epoch}: 验证损失 = {val_loss}")
        
        if val_loss < best_loss:
            best_loss = val_loss
            patience_counter = 0
            print(f"  -> 新的最佳损失: {best_loss}")
        else:
            patience_counter += 1
            print(f"  -> 没有改善，耐心计数器: {patience_counter}/{patience}")
            
        if patience_counter >= patience:
            print(f"  -> 早停触发！在第 {epoch} 轮停止")
            break

if __name__ == "__main__":
    test_auc_calculation()
    test_early_stopping_logic()
    print("\n测试完成！")
