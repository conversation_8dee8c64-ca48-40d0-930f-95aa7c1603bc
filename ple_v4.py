from torch.utils.data import Dataset
import numpy as np
import torch
from torch import nn
from torchmetrics import AUROC
import faulthandler
faulthandler.enable()


class ResidualBlock(nn.Module):
    def __init__(self, vec_dim):
        super().__init__()
        layers = []
        layers.append(nn.BatchNorm1d(vec_dim))
        layers.append(nn.ReLU())
        layers.append(nn.Linear(vec_dim, vec_dim))
        layers.append(nn.BatchNorm1d(vec_dim))
        layers.append(nn.ReLU())
        layers.append(nn.Linear(vec_dim, vec_dim))

        self.block = nn.Sequential(*layers)
        self.block.apply(ResidualBlock._init_weights)

    @staticmethod
    def _init_weights(layer):
        if isinstance(layer, nn.Linear):
            nn.init.xavier_normal_(layer.weight)
            # layer.bias.data.fill_(0)

    def forward(self, x):
        return x + self.block(x)




class TowerBlock(nn.Module):
    def __init__(self, vec_dim, output_dim, drop_rate=0.1):
        super().__init__()

        # 增加tower的容量
        hidden_dim1 = vec_dim // 2
        hidden_dim2 = vec_dim // 4

        layers = []
        layers.append(nn.BatchNorm1d(vec_dim))
        layers.append(nn.Linear(vec_dim, hidden_dim1))
        layers.append(nn.PReLU())
        layers.append(nn.BatchNorm1d(hidden_dim1))
        layers.append(nn.Dropout(p=drop_rate))

        # 添加第二个隐藏层
        layers.append(nn.Linear(hidden_dim1, hidden_dim2))
        layers.append(nn.PReLU())
        layers.append(nn.BatchNorm1d(hidden_dim2))
        layers.append(nn.Dropout(p=drop_rate * 0.5))

        layers.append(nn.Linear(hidden_dim2, output_dim))
        layers.append(nn.Sigmoid())

        self.block = nn.Sequential(*layers)
        self.block.apply(TowerBlock._init_weights)

    @staticmethod
    def _init_weights(layer):
        if isinstance(layer, nn.Linear):
            nn.init.xavier_normal_(layer.weight)
            # layer.bias.data.fill_(0)

    def forward(self, x):
        return self.block(x)



class RecsysDataset(Dataset):
    def __init__(self, X, y):
        assert len(X) == len(y)
        self.in_mem_X = torch.FloatTensor(X)
        assert self.in_mem_X.size(1) == 11
        self.in_mem_y = torch.FloatTensor(y)
        assert self.in_mem_y.size(1) == 2
        self.size = len(X)

    def __len__(self):
        return self.size

    def __getitem__(self, idx):
        user_id = self.in_mem_X[idx, 0].long()
        item_id = self.in_mem_X[idx, 1].long()        
        gender = self.in_mem_X[idx, 2].long()
        age = self.in_mem_X[idx, 3].long()
        item_cols = self.in_mem_X[idx, 4:]
        return (
            (item_id, gender, age, item_cols, ), self.in_mem_y[idx],
        )

class Expert_net(nn.Module):
    def __init__(self,input_dim, output_dim, dropout_rate=0.3, expert_hidden_layers=[128,64,32]): #增加网络深度和宽度，增加dropout
        super(Expert_net, self).__init__()

        self.expert_layer1 = nn.Sequential(
                            nn.Linear(input_dim, expert_hidden_layers[0]),
                            nn.BatchNorm1d(expert_hidden_layers[0]),  # 添加BatchNorm
                            nn.ReLU(),
                            nn.Dropout(dropout_rate),
        )
        self.expert_layer2 = nn.Sequential(
                            nn.Linear(expert_hidden_layers[0], expert_hidden_layers[1]),
                            nn.BatchNorm1d(expert_hidden_layers[1]),  # 添加BatchNorm
                            nn.ReLU(),
                            nn.Dropout(dropout_rate),
        )
        self.expert_layer3 = nn.Sequential(
                            nn.Linear(expert_hidden_layers[1], expert_hidden_layers[2]),
                            nn.BatchNorm1d(expert_hidden_layers[2]),  # 添加BatchNorm
                            nn.ReLU(),
                            nn.Dropout(dropout_rate),
        )
        self.expert_layer4 = nn.Sequential(
                            nn.Linear(expert_hidden_layers[2], output_dim),
                            nn.ReLU(),
                            nn.Dropout(dropout_rate * 0.5),  # 最后一层使用较小的dropout
        )
    def forward(self, x):
        out1 = self.expert_layer1(x)
        out2 = self.expert_layer2(out1)
        out3 = self.expert_layer3(out2)
        out4 = self.expert_layer4(out3)
        return out1, out2, out3, out4

'''特征提取层'''
class Extraction_Network(nn.Module):
    '''FeatureDim-输入数据的维数; ExpertOutDim-每个Expert输出的维数; TaskExpertNum-任务特定专家数;
       CommonExpertNum-共享专家数; GateNum-gate数(2表示最后一层，3表示中间层)'''
    def __init__(self, FeatureDim, ExpertOutDim, TaskExpertNum=1, CommonExpertNum=1, GateNum=2): 
        super(Extraction_Network, self).__init__()
        
        self.GateNum = GateNum #输出几个Gate的结果，2表示最后一层只输出两个任务的Gate，3表示还要输出中间共享层的Gate
        
        '''两个任务模块，一个共享模块'''
        self.n_task = 2
        self.n_share = 1
        
        expert_hidden_layers = [128, 64, 32]
        expert_out_dim = sum(expert_hidden_layers) + ExpertOutDim

        self.Experts_A = Expert_net(FeatureDim, ExpertOutDim, expert_hidden_layers=expert_hidden_layers)
        '''Shared-Experts'''

        self.Experts_Shared = Expert_net(FeatureDim, ExpertOutDim, expert_hidden_layers=expert_hidden_layers)


        self.Experts_B = Expert_net(FeatureDim, ExpertOutDim, expert_hidden_layers=expert_hidden_layers)
   
        self.Task_Gate1 = nn.Sequential(nn.Linear(FeatureDim, TaskExpertNum + CommonExpertNum), nn.Softmax(dim=1))
        self.Task_Gate2 = nn.Sequential(nn.Linear(FeatureDim, TaskExpertNum + CommonExpertNum), nn.Softmax(dim=1))
        self.Shared_Gates = nn.Sequential(nn.Linear(FeatureDim, 2 * TaskExpertNum + CommonExpertNum), nn.Softmax(dim=1))

        self.trans_layer_A = nn.Linear(expert_out_dim, ExpertOutDim)
        self.trans_layer_B = nn.Linear(expert_out_dim, ExpertOutDim)
        self.trans_layer_share = nn.Linear(expert_out_dim, ExpertOutDim)

    def forward(self, x_A, x_S, x_B):
        """
            x_A, x_S, x_B分别为taskA, share, taskB的不同输入
        """
        '''Experts_A模块输出'''
        Experts_A_Out = self.Experts_A(x_A)
        Experts_Shared_Out = self.Experts_Shared(x_S)
        Experts_B_Out = self.Experts_B(x_B)


        Gate_A = self.Task_Gate1(x_A)     # 维度 n_task个(bs,TaskExpertNum+CommonExpertNum)
        Gate_B = self.Task_Gate2(x_B)     # 维度 n_task个(bs,TaskExpertNum+CommonExpertNum)

        Gate_A_Out = self.expert_aggregate([Experts_A_Out, Experts_Shared_Out], Gate_A)
        Gate_A_Out = self.trans_layer_A(Gate_A_Out)

        Gate_B_Out = self.expert_aggregate([Experts_B_Out, Experts_Shared_Out], Gate_B)
        Gate_B_Out = self.trans_layer_B(Gate_B_Out)


        if self.GateNum == 3:
            Gate_Shared = self.Shared_Gates(x_S)     # 维度 n_task个(bs,2*TaskExpertNum+CommonExpertNum)
            Gate_Shared_Out = self.expert_aggregate([Experts_A_Out, Experts_Shared_Out, Experts_B_Out], Gate_Shared)
            Gate_Shared_Out = self.trans_layer_share(Gate_Shared_Out)
            return Gate_A_Out, Gate_Shared_Out, Gate_B_Out

        return Gate_A_Out,Gate_B_Out

    def expert_aggregate(self, expert_list, gate_score):
        """
            expert_list: element is (emb1, emb2, emb3)
            gate_scores: shape=(bs, nums)
        """
        n_layers = len(expert_list[0])
        gate_score = gate_score.unsqueeze(2)
        aggregate_emb = []
        for i in range(n_layers):
            layer_embs = [expert_list[j][i].unsqueeze(1) for j in range(len(expert_list))] 
            layer_embs = torch.concat(layer_embs, axis=1) # (bs, 3, dim)
            emb_out = torch.matmul(gate_score.transpose(1, 2), layer_embs).squeeze(1) # (bs, dim)
            aggregate_emb.append(emb_out)
        aggregate_emb = torch.concat(aggregate_emb, axis=-1) # (bs, 3 * dim)
        return aggregate_emb




class PLE_V4(nn.Module):
    def __init__(
        self,
        item_num,
        gender_num,
        age_num,
        item_dim,
        n_output,
        embedding_dim=64,  # 增加embedding维度
        device=torch.device('cpu')        
    ):
        super().__init__()
        # 增强dense特征处理
        self.dense_feature_layer = nn.Sequential(
            nn.Linear(7, embedding_dim),
            nn.BatchNorm1d(embedding_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(embedding_dim, embedding_dim)
        )
        # item_emb_dim = int(np.floor(np.log2(item_num)))
        self.item_embed = nn.Embedding(item_num, embedding_dim)

        # gender_dim = int(np.floor(np.log2(gender_num)))
        self.gender_embed = nn.Embedding(gender_num, embedding_dim)

        # age_dim = int(np.floor(np.log2(age_num)))
        self.age_embed = nn.Embedding(age_num, embedding_dim)

        input_cols = embedding_dim + embedding_dim + embedding_dim + embedding_dim + embedding_dim

        # 增加专家数量以提升模型容量
        # TaskExpertNum=2: 每个任务使用2个专家, CommonExpertNum=2: 使用2个共享专家
        self.Extraction_layer1 = Extraction_Network(input_cols, input_cols, TaskExpertNum=2, CommonExpertNum=2, GateNum=3).to(device=device)

        self.CGC = Extraction_Network(input_cols, input_cols, TaskExpertNum=2, CommonExpertNum=2, GateNum=2).to(device=device)

        # 增加tower的dropout以减少过拟合
        self.like_tower = TowerBlock(input_cols, 1, drop_rate = 0.4)
        self.share_tower = TowerBlock(input_cols, 1, drop_rate = 0.4)

        # self.init_weights()

    # def init_weights(layer):
        # nn.init.xavier_normal_(self.model_inter_layer.weight)
    def FM_module(self,  item_emb, gender_emb, age_emb, x_item_feature):
        features = [item_emb, gender_emb, age_emb, x_item_feature]
        features = [d.unsqueeze(1) for d in features]
        features = torch.concat(features, axis=1) # (BS, N, dim)
        FM_embedding = 0.5 * ((torch.sum(features, dim=1)) ** 2  - torch.sum(features ** 2, dim=1))
        return FM_embedding

    def forward(self, x_item_id, x_gender, x_age, x_item,):
        x_item_feature = self.dense_feature_layer(x_item)
        item_emb = self.item_embed(x_item_id)    
        gender_emb = self.gender_embed(x_gender)
        age_emb = self.age_embed(x_age)
        cross_emb = self.FM_module(item_emb, gender_emb, age_emb, x_item_feature)
        x_input = torch.cat((item_emb, gender_emb, age_emb, x_item_feature, cross_emb), dim=1)

        Output_A, Output_Shared, Output_B = self.Extraction_layer1(x_input, x_input, x_input) 
        Gate_A_Out,Gate_B_Out = self.CGC(Output_A, Output_Shared, Output_B)
        x_like = self.like_tower(Gate_A_Out)
        x_share = self.share_tower(Gate_B_Out)
        return torch.cat(
            (x_like, x_share), dim=1
        )



    # def configure_optimizers(self):
    #     optimizer = torch.optim.AdamW(
    #         self.parameters(), lr=3e-3, weight_decay=1e-2, amsgrad=True
    #     )
    #     return optimizer

