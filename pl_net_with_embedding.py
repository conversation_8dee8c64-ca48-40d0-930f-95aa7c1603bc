from torch.utils.data import Dataset
import numpy as np
import pytorch_lightning as pl
import torch
from torch import nn
from torchmetrics import AUROC
import faulthandler
faulthandler.enable()


class ResidualBlock(nn.Module):
    def __init__(self, vec_dim):
        super().__init__()
        layers = []
        layers.append(nn.BatchNorm1d(vec_dim))
        layers.append(nn.ReLU())
        layers.append(nn.Linear(vec_dim, vec_dim))
        layers.append(nn.BatchNorm1d(vec_dim))
        layers.append(nn.ReLU())
        layers.append(nn.Linear(vec_dim, vec_dim))

        self.block = nn.Sequential(*layers)
        self.block.apply(ResidualBlock._init_weights)

    @staticmethod
    def _init_weights(layer):
        if isinstance(layer, nn.Linear):
            nn.init.xavier_normal_(layer.weight)
            # layer.bias.data.fill_(0)

    def forward(self, x):
        return x + self.block(x)




class TowerBlock(nn.Module):
    def __init__(self, vec_dim, output_dim, drop_rate=0.1):
        super().__init__()

        hidden_dim = int(np.ceil(np.sqrt(vec_dim)))
        layers = []
        layers.append(nn.BatchNorm1d(vec_dim))
        layers.append(nn.Linear(vec_dim, hidden_dim))
        layers.append(nn.PReLU())
        layers.append(nn.BatchNorm1d(hidden_dim))
        layers.append(nn.Dropout(p=drop_rate))
        layers.append(nn.Linear(hidden_dim, output_dim))
        layers.append(nn.Sigmoid())

        self.block = nn.Sequential(*layers)
        self.block.apply(TowerBlock._init_weights)

    @staticmethod
    def _init_weights(layer):
        if isinstance(layer, nn.Linear):
            nn.init.xavier_normal_(layer.weight)
            # layer.bias.data.fill_(0)

    def forward(self, x):
        return self.block(x)


class RecsysDataset(Dataset):
    def __init__(self, X, y):
        assert len(X) == len(y)
        self.in_mem_X = torch.FloatTensor(X)
        assert self.in_mem_X.size(1) == 11
        self.in_mem_y = torch.FloatTensor(y)
        assert self.in_mem_y.size(1) == 2
        self.size = len(X)

    def __len__(self):
        return self.size

    def __getitem__(self, idx):
        user_id = self.in_mem_X[idx, 0].long()
        item_id = self.in_mem_X[idx, 1].long()        
        gender = self.in_mem_X[idx, 2].long()
        age = self.in_mem_X[idx, 3].long()
        item_cols = self.in_mem_X[idx, 4:]
        return (
            (
                item_id,
                gender,
                age,
                item_cols,
            ),
            self.in_mem_y[idx],
        )


class RecNet(pl.LightningModule):
    def __init__(
        self,
        item_num,
        gender_num,
        age_num,
        item_dim,
        n_output,        
    ):
        super().__init__()
        item_emb_dim = int(np.floor(np.log2(item_num)))
        self.item_embed = nn.Embedding(item_num, item_emb_dim)

        gender_dim = int(np.floor(np.log2(gender_num)))
        self.gender_embed = nn.Embedding(gender_num, gender_dim)

        age_dim = int(np.floor(np.log2(age_num)))
        self.age_embed = nn.Embedding(age_num, age_dim)

        input_cols = item_emb_dim + gender_dim + age_dim + item_dim
        self.like_tower = TowerBlock(input_cols, 1, drop_rate = 0.2)
        self.share_tower = TowerBlock(input_cols, 1, drop_rate = 0.2)
        self.roc_auc_metric = AUROC(task="binary")
        # self.init_weights()

    # def init_weights(layer):
        # nn.init.xavier_normal_(self.model_inter_layer.weight)

    def forward(
        self,
        x_item_id,
        x_gender,
        x_age,
        x_item,
    ):
        item_emb = self.item_embed(x_item_id)    
        gender_emb = self.gender_embed(x_gender)
        age_emb = self.age_embed(x_age)
        x_input = torch.cat((item_emb, gender_emb, age_emb, x_item), dim=1)
        x_like = self.like_tower(x_input)
        x_share = self.share_tower(x_input)
        return torch.cat(
            (x_like, x_share), dim=1
        )

    def training_step(self, batch, batch_idx):
        # training_step defined the train loop.
        # It is independent of forward
        (
            (
                x_item_id,                
                x_gender,
                x_age,
                x_item,
            ),
            y,
        ) = batch
        y_hat = self(
                x_item_id,              
                x_gender,
                x_age,
                x_item,
        )
        loss_like = self.loss(y_hat[:, 0], y[:, 0])
        loss_share = self.loss(y_hat[:, 1], y[:, 1]) 
        # t = 1   
        loss = loss_like + loss_share
        self.log("train_loss", loss)
        # for name,params in self.named_parameters():
        #     self.log(name, params.median())
        return {"loss": loss}

    def validation_step(self, batch, batch_idx):
        (
            (
                x_item_id,                                
                x_gender,
                x_age,
                x_item,
            ),
            y,
        ) = batch
        y_hat = self(
                x_item_id,                            
                x_gender,
                x_age,
                x_item,
        )

        loss_like = self.loss(y_hat[:, 0], y[:, 0])
        loss_share = self.loss(y_hat[:, 1], y[:, 1]) 
        loss = loss_like + loss_share
        auc_like = self.roc_auc_metric(y_hat[:, 0], y[:, 0].long())
        auc_share = self.roc_auc_metric(y_hat[:, 1], y[:, 1].long())
        results = {"val_loss": loss, "loss_share": loss_share, "loss_like": loss_like, 'auc_share': auc_share, 'auc_like': auc_like}
        for k, v in results.items():
            self.log(k, v)

        return results

    def predict_step(self, batch, batch_idx):
        (
            (
                x_item_id,                                
                x_gender,
                x_age,
                x_item,
            ),
            _,
        ) = batch
        y_hat = self(
                x_item_id,                            
                x_gender,
                x_age,
                x_item,
        )

        return {"pred": y_hat}

    def configure_optimizers(self):
        optimizer = torch.optim.AdamW(
            self.parameters(), lr=3e-3, weight_decay=1e-2, amsgrad=True
        )
        return optimizer

    def loss(self, preds, target):
        assert preds.size(0) == target.size(0)
        crossLoss = torch.nn.BCELoss()
        loss = crossLoss(preds, target)
        return loss
