nohup: ignoring input
/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/torchmetrics/utilities/prints.py:36: UserWarning: Metric `AUROC` will save all targets and predictions in buffer. For large datasets this may lead to large memory footprint.
  warnings.warn(*args, **kwargs)
Namespace(batch_size=256, checkpoint='checkpoint/ple_best.pth', device=device(type='cpu'), epoches=10, loss=BCELoss(), lr=0.001, model_type='ple', optimizer=Adam (
Parameter Group 0
    amsgrad: False
    betas: (0.9, 0.999)
    eps: 1e-08
    lr: 0.001
    weight_decay: 0
))
Epoch: 0/10, epoch_loss_like: 0.1291, epoch_loss_share: 0.1312    auc_like: 0.695392, auc_share: 0.617371
Test Result: 
Epoch: 0/10, epoch_loss_like: 0.0638, epoch_loss_share: 0.0565    auc_like: 0.856142, auc_share: 0.784891


Epoch: 1/10, epoch_loss_like: 0.0635, epoch_loss_share: 0.0561    auc_like: 0.856011, auc_share: 0.784346
Test Result: 
Epoch: 1/10, epoch_loss_like: 0.0625, epoch_loss_share: 0.0555    auc_like: 0.866698, auc_share: 0.802927


Epoch: 2/10, epoch_loss_like: 0.0626, epoch_loss_share: 0.0551    auc_like: 0.865199, auc_share: 0.804715
Test Result: 
Epoch: 2/10, epoch_loss_like: 0.0613, epoch_loss_share: 0.0541    auc_like: 0.877232, auc_share: 0.824405


Epoch: 3/10, epoch_loss_like: 0.0611, epoch_loss_share: 0.0539    auc_like: 0.879464, auc_share: 0.827569
Test Result: 
Epoch: 3/10, epoch_loss_like: 0.0600, epoch_loss_share: 0.0530    auc_like: 0.895380, auc_share: 0.852091


Epoch: 4/10, epoch_loss_like: 0.0592, epoch_loss_share: 0.0522    auc_like: 0.895857, auc_share: 0.852206
Test Result: 
Epoch: 4/10, epoch_loss_like: 0.0573, epoch_loss_share: 0.0509    auc_like: 0.909681, auc_share: 0.875369


Epoch: 5/10, epoch_loss_like: 0.0575, epoch_loss_share: 0.0506    auc_like: 0.907660, auc_share: 0.872881
Test Result: 
Epoch: 5/10, epoch_loss_like: 0.0556, epoch_loss_share: 0.0491    auc_like: 0.919602, auc_share: 0.890173


Epoch: 6/10, epoch_loss_like: 0.0560, epoch_loss_share: 0.0491    auc_like: 0.917032, auc_share: 0.887981
Test Result: 
Epoch: 6/10, epoch_loss_like: 0.0541, epoch_loss_share: 0.0475    auc_like: 0.927221, auc_share: 0.904502


Epoch: 7/10, epoch_loss_like: 0.0548, epoch_loss_share: 0.0477    auc_like: 0.923655, auc_share: 0.900292
Test Result: 
Epoch: 7/10, epoch_loss_like: 0.0533, epoch_loss_share: 0.0458    auc_like: 0.932898, auc_share: 0.914916


Epoch: 8/10, epoch_loss_like: 0.0536, epoch_loss_share: 0.0464    auc_like: 0.929400, auc_share: 0.910604
Test Result: 
Epoch: 8/10, epoch_loss_like: 0.0519, epoch_loss_share: 0.0446    auc_like: 0.937476, auc_share: 0.922527


Epoch: 9/10, epoch_loss_like: 0.0525, epoch_loss_share: 0.0453    auc_like: 0.933802, auc_share: 0.918151
Test Result: 
Epoch: 9/10, epoch_loss_like: 0.0509, epoch_loss_share: 0.0441    auc_like: 0.942273, auc_share: 0.929905


