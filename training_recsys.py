from pl_net_with_embedding import RecsysDataset, RecNet
from torch.utils.data import DataLoader
from datetime import datetime
import argparse
import random
import time
import os
import numpy as np
import pandas as pd
import torch
import argparse
from torchvision.transforms import ToTensor
from pytorch_lightning.callbacks.early_stopping import EarlyStopping
from pytorch_lightning.callbacks import ModelCheckpoint
from mmoe import MMOE_RecNet
from ple import PLE_RecNet
from ple_v2 import PLE_V2
from ple_v3 import PLE_V3
from ple_v4 import PLE_V4
from torchmetrics import AUROC
import faulthandler
faulthandler.enable()


def random_set():
    random.seed(930)
    torch.manual_seed(1010)
    np.random.seed(6563)

def metrics_auc(y_hat, y):
    roc_auc_metric = AUROC(task="binary")
    # 确保数据在CPU上并且类型正确
    y_hat_cpu = y_hat.cpu() if y_hat.is_cuda else y_hat
    y_cpu = y.cpu() if y.is_cuda else y

    # 确保预测值在[0,1]范围内，标签为整数类型
    auc_like = roc_auc_metric(y_hat_cpu[:, 0], y_cpu[:, 0].long())
    auc_share = roc_auc_metric(y_hat_cpu[:, 1], y_cpu[:, 1].long())
    return auc_like, auc_share

def print_epoch_log(epoch, epoch_loss_like, epoch_loss_share, epoch_pred, epoch_labels, args):
    epoch_pred = torch.concat(epoch_pred, axis=0)
    epoch_labels = torch.concat(epoch_labels, axis=0)
    epoch_loss_like = np.array(epoch_loss_like).mean()
    epoch_loss_share = np.array(epoch_loss_share).mean()
    auc_like, auc_share = metrics_auc(epoch_pred, epoch_labels)
    info = f"Epoch: {epoch}/{args.epoches}, epoch_loss_like: {'%.4f' % (epoch_loss_like)}, epoch_loss_share: {'%.4f' % (epoch_loss_share)}\
    auc_like: {'%.6f' % auc_like}, auc_share: {'%.6f' % (auc_share)}"
    print(info)

def eval(model, eval_dataloader, epoch, args):
    model.eval()
    with torch.no_grad():
        epoch_loss_like, epoch_loss_share, epoch_pred, epoch_labels = [], [], [], []
        for batch_index, batch_data in enumerate(eval_dataloader):  # 修复：使用eval_dataloader而不是train_dataloader
            ((x_item_id, x_gender, x_age, x_item, ), y,) = batch_data
            x_item_id = x_item_id.to(device=args.device)
            x_gender = x_gender.to(device=args.device)
            x_age = x_age.to(device=args.device)
            x_item = x_item.to(device=args.device)
            y = y.to(device=args.device)
            y_hat = model(x_item_id, x_gender, x_age, x_item,)

            loss_like = args.loss(y_hat[:, 0], y[:, 0])
            epoch_loss_like.append(loss_like.item())

            loss_share = args.loss(y_hat[:, 1], y[:, 1])
            epoch_loss_share.append(loss_share.item())
            epoch_pred.append(y_hat.cpu())  # 移到CPU以避免内存问题
            epoch_labels.append(y.cpu())    # 移到CPU以避免内存问题
            # auc_like, auc_share = metrics_auc(y_hat, y)
            # loss = loss_like + loss_share
    print("Test Result: ")
    print_epoch_log(epoch, epoch_loss_like, epoch_loss_share, epoch_pred, epoch_labels, args)
    print('\n')
    total_loss = np.array(epoch_loss_like).mean() + np.array(epoch_loss_share).mean()
    return total_loss

def Train(model, train_dataloader, eval_dataloader, args):
    best_eval_loss = 1e9
    patience_counter = 0
    patience = 5  # 早停机制：5轮没有改善就停止

    for epoch in range(args.epoches):
        model.train()  # 确保模型处于训练模式
        epoch_loss_like, epoch_loss_share, epoch_pred, epoch_labels = [], [], [], []
        for batch_index, batch_data in enumerate(train_dataloader):
            args.optimizer.zero_grad()
            # batch_data = batch_data.to(device=args.device)
            ((x_item_id, x_gender, x_age, x_item, ), y,) = batch_data
            x_item_id = x_item_id.to(device=args.device)
            x_gender = x_gender.to(device=args.device)
            x_age = x_age.to(device=args.device)
            x_item = x_item.to(device=args.device)
            y = y.to(device=args.device)
            y_hat = model(x_item_id, x_gender, x_age, x_item,)

            loss_like = args.loss(y_hat[:, 0], y[:, 0])
            epoch_loss_like.append(loss_like.item())

            loss_share = args.loss(y_hat[:, 1], y[:, 1])
            epoch_loss_share.append(loss_share.item())
            epoch_pred.append(y_hat.cpu().detach())  # 修复：移到CPU并detach
            epoch_labels.append(y.cpu().detach())    # 修复：移到CPU并detach
            # auc_like, auc_share = metrics_auc(y_hat, y)
            loss = loss_like + loss_share
            loss.backward()
            args.optimizer.step()
            # print(f"batch_index: {batch_index}, loss_like: {loss_like}, loss_share: {loss_share}, auc_like: {auc_like}, auc_share: {auc_share}")
        print_epoch_log(epoch, epoch_loss_like, epoch_loss_share, epoch_pred, epoch_labels, args)
        eval_loss = eval(model, eval_dataloader, epoch, args)

        # 早停机制
        if eval_loss < best_eval_loss:
            best_eval_loss = eval_loss
            patience_counter = 0
            torch.save(model.state_dict(), args.checkpoint)
            print(f"新的最佳模型已保存，验证损失: {best_eval_loss:.6f}")
        else:
            patience_counter += 1
            print(f"验证损失没有改善，耐心计数器: {patience_counter}/{patience}")

        if patience_counter >= patience:
            print(f"早停触发！在第 {epoch+1} 轮停止训练")
            break


            
if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--batch_size", type=int, default=512)    
    parser.add_argument('--epoches', type=int, default=10)
    parser.add_argument("--model_type", default='ple_v4', type=str)
    parser.add_argument('--lr', default=1e-3, type=float)
    parser.add_argument('--num_workers', default=8, type=int)
    
    args = parser.parse_args()
    ## Prepare data
    args.checkpoint = os.path.join('checkpoint', args.model_type+'_best.pth')
    os.makedirs('checkpoint', exist_ok=True)
    args.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    # args.device = torch.device('cpu')
    # checkpoint_path = args.checkpoint_path

    label_col = ["like", 'share']
    train = pd.read_csv(f"train.csv")
    valid = pd.read_csv(f"valid.csv")
   
    feature_cols = [
        'user_id',
        'item_id',
        'gender',
        'age',
        'click_count',
        'like_count',
        'comment_count',
        'read_percentage',
        'item_score1',
        'item_score2',
        'item_score3'
    ]

    X_train = train[feature_cols].values
    y_train = train[label_col].values

    X_eval = valid[feature_cols].values
    y_eval = valid[label_col].values

    eval_data = RecsysDataset(X_eval, y_eval)

    eval_dataloader = DataLoader(eval_data, batch_size=args.batch_size, num_workers=args.num_workers, shuffle=False)

    train_data = RecsysDataset(X_train, y_train)
    train_dataloader = DataLoader(train_data, batch_size=args.batch_size, num_workers=args.num_workers, shuffle=True)

    random_set()
    if args.model_type == 'ple':
        rec_net = PLE_RecNet(item_num = 230000, gender_num=3, age_num=8, item_dim=7, n_output=2, device=args.device).to(device=args.device)
    elif args.model_type == 'ple_v2':
        rec_net = PLE_V2(item_num = 230000, gender_num=3, age_num=8, item_dim=7, n_output=2, device=args.device).to(device=args.device)
    elif args.model_type == 'ple_v3':
        rec_net = PLE_V3(item_num = 230000, gender_num=3, age_num=8, item_dim=7, n_output=2, device=args.device).to(device=args.device)
    elif args.model_type == 'ple_v4':
        rec_net = PLE_V4(item_num = 230000, gender_num=3, age_num=8, item_dim=7, n_output=2, device=args.device).to(device=args.device)
    elif args.model_type == 'base':
        rec_net = RecNet(item_num = 230000, gender_num=3, age_num=8, item_dim=7, n_output=2).to(device=args.device)
    elif args.model_type == 'mmoe':
        rec_net = MMOE_RecNet(item_num = 230000, gender_num=3, age_num=8, item_dim=7, n_output=2).to(device=args.device)
    else:
        print("Error Model type...")
    args.loss = torch.nn.BCELoss()
    args.optimizer = torch.optim.Adam(rec_net.parameters(), lr=args.lr)
    print(args)
    Train(rec_net, train_dataloader, eval_dataloader, args)