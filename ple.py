from torch.utils.data import Dataset
import numpy as np
import pytorch_lightning as pl
import torch
from torch import nn
from torchmetrics import AUROC
import faulthandler
faulthandler.enable()


class ResidualBlock(nn.Module):
    def __init__(self, vec_dim):
        super().__init__()
        layers = []
        layers.append(nn.BatchNorm1d(vec_dim))
        layers.append(nn.ReLU())
        layers.append(nn.Linear(vec_dim, vec_dim))
        layers.append(nn.BatchNorm1d(vec_dim))
        layers.append(nn.ReLU())
        layers.append(nn.Linear(vec_dim, vec_dim))

        self.block = nn.Sequential(*layers)
        self.block.apply(ResidualBlock._init_weights)

    @staticmethod
    def _init_weights(layer):
        if isinstance(layer, nn.Linear):
            nn.init.xavier_normal_(layer.weight)
            # layer.bias.data.fill_(0)

    def forward(self, x):
        return x + self.block(x)




class TowerBlock(nn.Module):
    def __init__(self, vec_dim, output_dim, drop_rate=0.1):
        super().__init__()

        hidden_dim = int(np.ceil(np.sqrt(vec_dim)))
        layers = []
        layers.append(nn.BatchNorm1d(vec_dim))
        layers.append(nn.Linear(vec_dim, hidden_dim))
        layers.append(nn.PReLU())
        layers.append(nn.BatchNorm1d(hidden_dim))
        layers.append(nn.Dropout(p=drop_rate))
        layers.append(nn.Linear(hidden_dim, output_dim))
        layers.append(nn.Sigmoid())

        self.block = nn.Sequential(*layers)
        self.block.apply(TowerBlock._init_weights)

    @staticmethod
    def _init_weights(layer):
        if isinstance(layer, nn.Linear):
            nn.init.xavier_normal_(layer.weight)
            # layer.bias.data.fill_(0)

    def forward(self, x):
        return self.block(x)



class RecsysDataset(Dataset):
    def __init__(self, X, y):
        assert len(X) == len(y)
        self.in_mem_X = torch.FloatTensor(X)
        assert self.in_mem_X.size(1) == 11
        self.in_mem_y = torch.FloatTensor(y)
        assert self.in_mem_y.size(1) == 2
        self.size = len(X)

    def __len__(self):
        return self.size

    def __getitem__(self, idx):
        user_id = self.in_mem_X[idx, 0].long()
        item_id = self.in_mem_X[idx, 1].long()        
        gender = self.in_mem_X[idx, 2].long()
        age = self.in_mem_X[idx, 3].long()
        item_cols = self.in_mem_X[idx, 4:]
        return (
            (item_id, gender, age, item_cols, ), self.in_mem_y[idx],
        )

class Expert_net(nn.Module):
    def __init__(self,input_dim, output_dim, dropout_rate=0): #input_dim代表输入维度，output_dim代表输出维度
        super(Expert_net, self).__init__()
        # p=0
        expert_hidden_layers = [64,32]
        self.expert_layer = nn.Sequential(
                            nn.Linear(input_dim, expert_hidden_layers[0]),
                            nn.ReLU(),
                            nn.Dropout(dropout_rate),
                            nn.Linear(expert_hidden_layers[0], expert_hidden_layers[1]),
                            nn.ReLU(),
                            nn.Dropout(dropout_rate),
                            nn.Linear(expert_hidden_layers[1],output_dim),
                            nn.ReLU(),
                            nn.Dropout(dropout_rate)
                            )  

    def forward(self, x):
        # print("debug in 105 line, x type is ", x)
        # print("self.expert_layer.device", self.expert_layer.device)
        out = self.expert_layer(x)
        return out

'''特征提取层'''
class Extraction_Network(nn.Module):
    '''FeatureDim-输入数据的维数; ExpertOutDim-每个Expert输出的维数; TaskExpertNum-任务特定专家数;
       CommonExpertNum-共享专家数; GateNum-gate数(2表示最后一层，3表示中间层)'''
    def __init__(self,FeatureDim,ExpertOutDim,TaskExpertNum=1, CommonExpertNum=1, GateNum=2): 
        super(Extraction_Network, self).__init__()
        
        self.GateNum = GateNum #输出几个Gate的结果，2表示最后一层只输出两个任务的Gate，3表示还要输出中间共享层的Gate
        
        '''两个任务模块，一个共享模块'''
        self.n_task = 2
        self.n_share = 1
        
        '''TaskA-Experts'''
        for i in range(TaskExpertNum):
            setattr(self, "expert_layer"+str(i+1), Expert_net(FeatureDim,ExpertOutDim)) 
        self.Experts_A = [getattr(self,"expert_layer"+str(i+1)) for i in range(TaskExpertNum)]# Experts_A模块，给taskA分配 TaskExpertNum个Expert
        
        '''Shared-Experts'''
        for i in range(CommonExpertNum):
            setattr(self, "expert_layer"+str(i+1), Expert_net(FeatureDim,ExpertOutDim)) 
        self.Experts_Shared = [getattr(self,"expert_layer"+str(i+1)) for i in range(CommonExpertNum)]#Experts_Shared模块，CommonExpertNum个Expert
        
        '''TaskB-Experts'''
        for i in range(TaskExpertNum):
            setattr(self, "expert_layer"+str(i+1), Expert_net(FeatureDim,ExpertOutDim)) 
        self.Experts_B = [getattr(self,"expert_layer"+str(i+1)) for i in range(TaskExpertNum)]#Experts_B模块， 给taskB分配 TaskExpertNum个Expert
        
        '''Task_Gate网络结构'''
        for i in range(self.n_task):
            setattr(self, "gate_layer"+str(i+1), nn.Sequential(nn.Linear(FeatureDim, TaskExpertNum + CommonExpertNum), nn.Softmax(dim=1))) 
        self.Task_Gates = [getattr(self,"gate_layer"+str(i+1)) for i in range(self.n_task)]#为每个gate创建一个lr+softmax      
        
        '''Shared_Gate网络结构'''
        for i in range(self.n_share):
            setattr(self, "gate_layer"+str(i+1), nn.Sequential(nn.Linear(FeatureDim, 2 * TaskExpertNum + CommonExpertNum), nn.Softmax(dim=1))) 
        self.Shared_Gates = [getattr(self,"gate_layer"+str(i+1)) for i in range(self.n_share)]#共享gate       
        
    def forward(self, x_A, x_S, x_B):
        """
            x_A, x_S, x_B分别为taskA, share, taskB的不同输入
        """
        '''Experts_A模块输出'''
        Experts_A_Out = [expert(x_A) for expert in self.Experts_A] #
        Experts_A_Out = torch.cat(([expert[:,np.newaxis,:] for expert in Experts_A_Out]),dim = 1) # 维度 (bs, TaskExpertNum, ExpertOutDim)
        
        '''Experts_Shared模块输出'''
        Experts_Shared_Out = [expert(x_S) for expert in self.Experts_Shared] #
        Experts_Shared_Out = torch.cat(([expert[:, np.newaxis, :] for expert in Experts_Shared_Out]), dim = 1) # 维度 (bs,CommonExpertNum,ExpertOutDim)
        
        '''Experts_B模块输出'''
        Experts_B_Out = [expert(x_B) for expert in self.Experts_B] #
        Experts_B_Out = torch.cat(([expert[:,np.newaxis,:] for expert in Experts_B_Out]),dim = 1) # 维度 (bs, TaskExpertNum, ExpertOutDim)
        
        '''Gate_A的权重'''
        Gate_A = self.Task_Gates[0](x_A)     # 维度 n_task个(bs,TaskExpertNum+CommonExpertNum)
        '''Gate_Shared的权重'''
        if self.GateNum == 3:
            Gate_Shared = self.Shared_Gates[0](x_S)     # 维度 n_task个(bs,2*TaskExpertNum+CommonExpertNum)
        '''Gate_B的权重'''
        Gate_B = self.Task_Gates[1](x_B)     # 维度 n_task个(bs,TaskExpertNum+CommonExpertNum)
             
        '''GateA输出'''
        g = Gate_A.unsqueeze(2)  # 维度(bs, TaskExpertNum+CommonExpertNum, 1)
        experts = torch.cat([Experts_A_Out,Experts_Shared_Out],dim=1) #维度(bs, TaskExpertNum+CommonExpertNum,ExpertOutDim)
        Gate_A_Out = torch.matmul(experts.transpose(1,2), g)#维度(bs, ExpertOutDim, 1)
        Gate_A_Out = Gate_A_Out.squeeze(2)#维度(bs, ExpertOutDim)  
        '''GateShared输出'''
        if self.GateNum == 3:
            g = Gate_Shared.unsqueeze(2)  # 维度(bs,2*TaskExpertNum+CommonExpertNum,1)
            experts = torch.cat([Experts_A_Out, Experts_Shared_Out, Experts_B_Out], dim=1) #维度(bs,2*TaskExpertNum+CommonExpertNum,ExpertOutDim)
            Gate_Shared_Out = torch.matmul(experts.transpose(1,2),g)#维度(bs,ExpertOutDim,1)
            Gate_Shared_Out = Gate_Shared_Out.squeeze(2)#维度(bs,ExpertOutDim)        
        '''GateB输出'''
        g = Gate_B.unsqueeze(2)  # 维度(bs,TaskExpertNum+CommonExpertNum,1)
        experts = torch.cat([Experts_B_Out,Experts_Shared_Out],dim=1) #维度(bs,TaskExpertNum+CommonExpertNum,ExpertOutDim)
        Gate_B_Out = torch.matmul(experts.transpose(1,2), g)#维度(bs,ExpertOutDim,1)
        Gate_B_Out = Gate_B_Out.squeeze(2)#维度(bs,ExpertOutDim)
        
        if self.GateNum == 3:
            return Gate_A_Out,Gate_Shared_Out,Gate_B_Out
        else:
            return Gate_A_Out,Gate_B_Out


# class Expert_Gate(nn.Module):
#     def __init__(self, feature_dim, expert_dim, n_expert=3, n_task=2, use_gate=True): 
#         """
#             feature_dim: dimension of input
#             expert_dim: dimension of expert output
#         """
#         #feature_dim:输入数据的维数  expert_dim:每个神经元输出的维数  n_expert:专家数量  n_task:任务数(gate数)  use_gate：是否使用门控，如果不使用则各个专家取平均
#         super(Expert_Gate, self).__init__()
#         self.n_task = n_task
#         self.use_gate = use_gate
        
#         '''专家网络'''
#         for i in range(n_expert):
#             setattr(self, "expert_layer"+str(i+1), Expert(feature_dim,expert_dim)) 
#         self.expert_layers = [getattr(self,"expert_layer"+str(i+1)) for i in range(n_expert)]#为每个expert创建一个DNN
        
#         '''门控网络'''
#         for i in range(n_task):
#             setattr(self, "gate_layer"+str(i+1), nn.Sequential(nn.Linear(feature_dim, n_expert),
#                                         					   nn.Softmax(dim=1))) 
#         self.gate_layers = [getattr(self,"gate_layer"+str(i+1)) for i in range(n_task)]#为每个gate创建一个lr+softmax
        
#     def forward(self, x):
#         if self.use_gate:
#             # 构建多个专家网络
#             E_net = [expert(x) for expert in self.expert_layers]
#             E_net = torch.cat(([e[:,np.newaxis,:] for e in E_net]),dim = 1) # 维度 (bs,n_expert,expert_dim)

#             # 构建多个门网络
#             gate_net = [gate(x) for gate in self.gate_layers]     # 维度 n_task个(bs,n_expert)

#             # towers计算：对应的门网络乘上所有的专家网络
#             towers = []
#             for i in range(self.n_task):
#                 g = gate_net[i].unsqueeze(2)  # 维度(bs,n_expert,1)
#                 tower = torch.matmul(E_net.transpose(1,2),g)# 维度 (bs,expert_dim,1)
#                 towers.append(tower.transpose(1,2).squeeze(1))           # 维度(bs,expert_dim)
#         else:
#             E_net = [expert(x) for expert in self.expert_layers]
#             towers = sum(E_net)/len(E_net)
#         return towers


class PLE_RecNet(nn.Module):
    def __init__(
        self,
        item_num,
        gender_num,
        age_num,
        item_dim,
        n_output,
        device=torch.device('cpu')        
    ):
        super().__init__()
        item_emb_dim = int(np.floor(np.log2(item_num)))
        self.item_embed = nn.Embedding(item_num, item_emb_dim)

        gender_dim = int(np.floor(np.log2(gender_num)))
        self.gender_embed = nn.Embedding(gender_num, gender_dim)

        age_dim = int(np.floor(np.log2(age_num)))
        self.age_embed = nn.Embedding(age_num, age_dim)

        input_cols = item_emb_dim + gender_dim + age_dim + item_dim
        # input_cols = input_cols.to(device=device)
        # input_cols代表输入的特征维度，第二个input_cols代表输出的特征维度，TaskExperNum代表每个任务需要用几个专家（可调参数），CommonExpertNum代表有几个共享专家（可调参数）
        # GateNum最好不要动，3的意思是2个task_experts的输出加上一个shared_experts输出. 2代表不需要shared_experts输出，具体逻辑去看Extraction_Network的forward最后输出
        self.Extraction_layer1 = Extraction_Network(input_cols, input_cols, TaskExpertNum=1, CommonExpertNum=1, GateNum=3).to(device=device)

        self.CGC = Extraction_Network(input_cols,input_cols, TaskExpertNum=1, CommonExpertNum=1, GateNum=2).to(device=device)

        self.like_tower = TowerBlock(input_cols, 1, drop_rate = 0.2)
        self.share_tower = TowerBlock(input_cols, 1, drop_rate = 0.2)
        self.roc_auc_metric = AUROC(task="binary")
        # self.init_weights()

    # def init_weights(layer):
        # nn.init.xavier_normal_(self.model_inter_layer.weight)
    
    def forward(self, x_item_id, x_gender, x_age, x_item,):
        item_emb = self.item_embed(x_item_id)    
        gender_emb = self.gender_embed(x_gender)
        age_emb = self.age_embed(x_age)
        x_input = torch.cat((item_emb, gender_emb, age_emb, x_item), dim=1) # is cuda 
        # print("debug in 276 lines", x_input)
        # expert_ouput = self.expert_gate(x_input)
        Output_A, Output_Shared, Output_B = self.Extraction_layer1(x_input, x_input, x_input) 
        Gate_A_Out,Gate_B_Out = self.CGC(Output_A, Output_Shared, Output_B)
        x_like = self.like_tower(Gate_A_Out)
        x_share = self.share_tower(Gate_B_Out)
        return torch.cat(
            (x_like, x_share), dim=1
        )

    def training_step(self, batch, batch_idx):
        # training_step defined the train loop.
        # It is independent of forward
        ((x_item_id, x_gender, x_age, x_item, ), y,) = batch
        y_hat = self(x_item_id, x_gender, x_age, x_item,)
        loss_like = self.loss(y_hat[:, 0], y[:, 0])
        loss_share = self.loss(y_hat[:, 1], y[:, 1]) 
        # t = 1   
        loss = loss_like + loss_share
        self.log("train_loss", loss)
        # for name,params in self.named_parameters():
        #     self.log(name, params.median())
        return {"loss": loss}

    def validation_step(self, batch, batch_idx):
        ((x_item_id, x_gender, x_age, x_item,), y,) = batch
        y_hat = self(x_item_id, x_gender, x_age, x_item,)

        loss_like = self.loss(y_hat[:, 0], y[:, 0])
        loss_share = self.loss(y_hat[:, 1], y[:, 1]) 
        loss = loss_like + loss_share
        auc_like = self.roc_auc_metric(y_hat[:, 0], y[:, 0].long())
        auc_share = self.roc_auc_metric(y_hat[:, 1], y[:, 1].long())
        results = {"val_loss": loss, "loss_share": loss_share, "loss_like": loss_like, "auc_share": auc_share, "auc_like": auc_like}
        for k, v in results.items():
            self.log(k, v)

        return results

    def predict_step(self, batch, batch_idx):
        ((x_item_id, x_gender, x_age, x_item,),  _,) = batch
        y_hat = self(x_item_id, x_gender, x_age, x_item,)

        return {"pred": y_hat}

    def configure_optimizers(self):
        optimizer = torch.optim.AdamW(
            self.parameters(), lr=3e-3, weight_decay=1e-2, amsgrad=True
        )
        return optimizer

    def loss(self, preds, target):
        assert preds.size(0) == target.size(0)
        crossLoss = torch.nn.BCELoss()
        loss = crossLoss(preds, target)
        return loss
