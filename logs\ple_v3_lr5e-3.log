nohup: ignoring input
Namespace(batch_size=256, checkpoint='checkpoint/ple_v3_best.pth', device=device(type='cuda'), epoches=10, loss=BCELoss(), lr=0.005, model_type='ple_v3', optimizer=Adam (
Parameter Group 0
    amsgrad: False
    betas: (0.9, 0.999)
    eps: 1e-08
    lr: 0.005
    weight_decay: 0
))
/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/torchmetrics/utilities/prints.py:36: UserWarning: Metric `AUROC` will save all targets and predictions in buffer. For large datasets this may lead to large memory footprint.
  warnings.warn(*args, **kwargs)
Epoch: 0/10, epoch_loss_like: 0.0768, epoch_loss_share: 0.0680    auc_like: 0.815135, auc_share: 0.752901
Test Result: 
Epoch: 0/10, epoch_loss_like: 0.0666, epoch_loss_share: 0.0545    auc_like: 0.850132, auc_share: 0.822085


Epoch: 1/10, epoch_loss_like: 0.0617, epoch_loss_share: 0.0538    auc_like: 0.874839, auc_share: 0.831001
Test Result: 
Epoch: 1/10, epoch_loss_like: 0.0593, epoch_loss_share: 0.0506    auc_like: 0.907676, auc_share: 0.892825


Epoch: 2/10, epoch_loss_like: 0.0567, epoch_loss_share: 0.0486    auc_like: 0.913977, auc_share: 0.894192
Test Result: 
Epoch: 2/10, epoch_loss_like: 0.0530, epoch_loss_share: 0.0448    auc_like: 0.938643, auc_share: 0.931450


Epoch: 3/10, epoch_loss_like: 0.0527, epoch_loss_share: 0.0440    auc_like: 0.934140, auc_share: 0.927125
Test Result: 
Epoch: 3/10, epoch_loss_like: 0.0483, epoch_loss_share: 0.0410    auc_like: 0.950379, auc_share: 0.949385


Epoch: 4/10, epoch_loss_like: 0.0496, epoch_loss_share: 0.0415    auc_like: 0.945138, auc_share: 0.941662
Test Result: 
Epoch: 4/10, epoch_loss_like: 0.0455, epoch_loss_share: 0.0382    auc_like: 0.957687, auc_share: 0.958969


Epoch: 5/10, epoch_loss_like: 0.0474, epoch_loss_share: 0.0399    auc_like: 0.951784, auc_share: 0.948735
Test Result: 
Epoch: 5/10, epoch_loss_like: 0.0438, epoch_loss_share: 0.0382    auc_like: 0.962270, auc_share: 0.958731


Epoch: 6/10, epoch_loss_like: 0.0454, epoch_loss_share: 0.0384    auc_like: 0.957000, auc_share: 0.954138
Test Result: 
Epoch: 6/10, epoch_loss_like: 0.0413, epoch_loss_share: 0.0364    auc_like: 0.966348, auc_share: 0.964499


Epoch: 7/10, epoch_loss_like: 0.0434, epoch_loss_share: 0.0367    auc_like: 0.961580, auc_share: 0.959716
Test Result: 
Epoch: 7/10, epoch_loss_like: 0.0402, epoch_loss_share: 0.0340    auc_like: 0.969592, auc_share: 0.969429


Epoch: 8/10, epoch_loss_like: 0.0420, epoch_loss_share: 0.0358    auc_like: 0.964823, auc_share: 0.962547
Test Result: 
Epoch: 8/10, epoch_loss_like: 0.0393, epoch_loss_share: 0.0325    auc_like: 0.972216, auc_share: 0.973291


Epoch: 9/10, epoch_loss_like: 0.0406, epoch_loss_share: 0.0345    auc_like: 0.967831, auc_share: 0.966315
Test Result: 
Epoch: 9/10, epoch_loss_like: 0.0385, epoch_loss_share: 0.0324    auc_like: 0.974408, auc_share: 0.974660


