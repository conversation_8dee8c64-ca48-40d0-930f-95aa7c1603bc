{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {"collapsed": false}, "outputs": [], "source": ["from joblib import load\n", "import numpy as np\n", "import pandas as pd\n", "import pytz\n", "from datetime import datetime\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import time\n", "from sklearn.preprocessing import LabelEncoder, MinMaxScaler, StandardScaler"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"collapsed": false}, "outputs": [], "source": ["# data = pd.read_csv(r\"C:\\Users\\<USER>\\Desktop\\Tenrec\\cold_data_0.3.csv\")\n", "data = pd.read_csv(\"cold_data_0.3.csv\")"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>item_id</th>\n", "      <th>click</th>\n", "      <th>gender</th>\n", "      <th>age</th>\n", "      <th>click_count</th>\n", "      <th>like_count</th>\n", "      <th>comment_count</th>\n", "      <th>read_percentage</th>\n", "      <th>item_score1</th>\n", "      <th>item_score2</th>\n", "      <th>category_second</th>\n", "      <th>category_first</th>\n", "      <th>item_score3</th>\n", "      <th>read_time</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1048575.0</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>6.017558e+05</td>\n", "      <td>1.100806e+05</td>\n", "      <td>1.0</td>\n", "      <td>1.318270e+00</td>\n", "      <td>2.674438e+00</td>\n", "      <td>1.435386e+05</td>\n", "      <td>1.896691e+03</td>\n", "      <td>7.698966e+02</td>\n", "      <td>4.944988e+01</td>\n", "      <td>3.006760e+00</td>\n", "      <td>3.070605e+00</td>\n", "      <td>1.139182e+04</td>\n", "      <td>1.142028e+02</td>\n", "      <td>4.570341e+00</td>\n", "      <td>5.291009e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.272428e+05</td>\n", "      <td>7.086640e+04</td>\n", "      <td>0.0</td>\n", "      <td>4.706243e-01</td>\n", "      <td>1.273393e+00</td>\n", "      <td>3.411997e+05</td>\n", "      <td>5.990057e+03</td>\n", "      <td>1.633480e+03</td>\n", "      <td>2.889158e+01</td>\n", "      <td>7.726339e-01</td>\n", "      <td>6.545640e-01</td>\n", "      <td>1.578161e+03</td>\n", "      <td>1.485303e+01</td>\n", "      <td>4.045910e+00</td>\n", "      <td>7.584976e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.100000e+01</td>\n", "      <td>1.480000e+02</td>\n", "      <td>1.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2.936360e+05</td>\n", "      <td>2.618300e+04</td>\n", "      <td>1.0</td>\n", "      <td>1.000000e+00</td>\n", "      <td>2.000000e+00</td>\n", "      <td>1.316000e+04</td>\n", "      <td>7.300000e+01</td>\n", "      <td>5.600000e+01</td>\n", "      <td>2.700000e+01</td>\n", "      <td>3.000000e+00</td>\n", "      <td>3.000000e+00</td>\n", "      <td>1.080200e+04</td>\n", "      <td>1.080000e+02</td>\n", "      <td>3.000000e+00</td>\n", "      <td>7.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>6.902920e+05</td>\n", "      <td>1.382030e+05</td>\n", "      <td>1.0</td>\n", "      <td>1.000000e+00</td>\n", "      <td>2.000000e+00</td>\n", "      <td>4.648600e+04</td>\n", "      <td>2.960000e+02</td>\n", "      <td>2.520000e+02</td>\n", "      <td>4.700000e+01</td>\n", "      <td>3.000000e+00</td>\n", "      <td>3.000000e+00</td>\n", "      <td>1.120200e+04</td>\n", "      <td>1.120000e+02</td>\n", "      <td>5.000000e+00</td>\n", "      <td>2.600000e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>9.253360e+05</td>\n", "      <td>1.674855e+05</td>\n", "      <td>1.0</td>\n", "      <td>2.000000e+00</td>\n", "      <td>3.000000e+00</td>\n", "      <td>1.341140e+05</td>\n", "      <td>1.042000e+03</td>\n", "      <td>8.230000e+02</td>\n", "      <td>6.700000e+01</td>\n", "      <td>4.000000e+00</td>\n", "      <td>3.000000e+00</td>\n", "      <td>1.210800e+04</td>\n", "      <td>1.210000e+02</td>\n", "      <td>6.000000e+00</td>\n", "      <td>7.000000e+01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.988720e+05</td>\n", "      <td>2.272830e+05</td>\n", "      <td>1.0</td>\n", "      <td>2.000000e+00</td>\n", "      <td>7.000000e+00</td>\n", "      <td>6.824316e+06</td>\n", "      <td>1.516560e+05</td>\n", "      <td>4.567700e+04</td>\n", "      <td>5.780000e+02</td>\n", "      <td>4.000000e+00</td>\n", "      <td>9.000000e+00</td>\n", "      <td>1.410200e+04</td>\n", "      <td>1.410000e+02</td>\n", "      <td>2.550000e+02</td>\n", "      <td>4.446000e+03</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            user_id       item_id      click        gender           age  \\\n", "count  1.048575e+06  1.048575e+06  1048575.0  1.048575e+06  1.048575e+06   \n", "mean   6.017558e+05  1.100806e+05        1.0  1.318270e+00  2.674438e+00   \n", "std    3.272428e+05  7.086640e+04        0.0  4.706243e-01  1.273393e+00   \n", "min    1.100000e+01  1.480000e+02        1.0  0.000000e+00  0.000000e+00   \n", "25%    2.936360e+05  2.618300e+04        1.0  1.000000e+00  2.000000e+00   \n", "50%    6.902920e+05  1.382030e+05        1.0  1.000000e+00  2.000000e+00   \n", "75%    9.253360e+05  1.674855e+05        1.0  2.000000e+00  3.000000e+00   \n", "max    9.988720e+05  2.272830e+05        1.0  2.000000e+00  7.000000e+00   \n", "\n", "        click_count    like_count  comment_count  read_percentage  \\\n", "count  1.048575e+06  1.048575e+06   1.048575e+06     1.048575e+06   \n", "mean   1.435386e+05  1.896691e+03   7.698966e+02     4.944988e+01   \n", "std    3.411997e+05  5.990057e+03   1.633480e+03     2.889158e+01   \n", "min    0.000000e+00  0.000000e+00   0.000000e+00     0.000000e+00   \n", "25%    1.316000e+04  7.300000e+01   5.600000e+01     2.700000e+01   \n", "50%    4.648600e+04  2.960000e+02   2.520000e+02     4.700000e+01   \n", "75%    1.341140e+05  1.042000e+03   8.230000e+02     6.700000e+01   \n", "max    6.824316e+06  1.516560e+05   4.567700e+04     5.780000e+02   \n", "\n", "        item_score1   item_score2  category_second  category_first  \\\n", "count  1.048575e+06  1.048575e+06     1.048575e+06    1.048575e+06   \n", "mean   3.006760e+00  3.070605e+00     1.139182e+04    1.142028e+02   \n", "std    7.726339e-01  6.545640e-01     1.578161e+03    1.485303e+01   \n", "min    0.000000e+00  0.000000e+00     0.000000e+00    0.000000e+00   \n", "25%    3.000000e+00  3.000000e+00     1.080200e+04    1.080000e+02   \n", "50%    3.000000e+00  3.000000e+00     1.120200e+04    1.120000e+02   \n", "75%    4.000000e+00  3.000000e+00     1.210800e+04    1.210000e+02   \n", "max    4.000000e+00  9.000000e+00     1.410200e+04    1.410000e+02   \n", "\n", "        item_score3     read_time  \n", "count  1.048575e+06  1.048575e+06  \n", "mean   4.570341e+00  5.291009e+01  \n", "std    4.045910e+00  7.584976e+01  \n", "min    0.000000e+00  0.000000e+00  \n", "25%    3.000000e+00  7.000000e+00  \n", "50%    5.000000e+00  2.600000e+01  \n", "75%    6.000000e+00  7.000000e+01  \n", "max    2.550000e+02  4.446000e+03  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>item_id</th>\n", "      <th>click</th>\n", "      <th>gender</th>\n", "      <th>age</th>\n", "      <th>click_count</th>\n", "      <th>like_count</th>\n", "      <th>comment_count</th>\n", "      <th>read_percentage</th>\n", "      <th>item_score1</th>\n", "      <th>item_score2</th>\n", "      <th>category_second</th>\n", "      <th>category_first</th>\n", "      <th>item_score3</th>\n", "      <th>read</th>\n", "      <th>read_time</th>\n", "      <th>share</th>\n", "      <th>like</th>\n", "      <th>follow</th>\n", "      <th>favorite</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>393219</td>\n", "      <td>73342</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>73</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>25</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>12000</td>\n", "      <td>120</td>\n", "      <td>1</td>\n", "      <td>True</td>\n", "      <td>4.0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>393219</td>\n", "      <td>17695</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>29552</td>\n", "      <td>209</td>\n", "      <td>84</td>\n", "      <td>43</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>11204</td>\n", "      <td>112</td>\n", "      <td>2</td>\n", "      <td>True</td>\n", "      <td>19.0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>393219</td>\n", "      <td>40457</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>20285</td>\n", "      <td>242</td>\n", "      <td>90</td>\n", "      <td>43</td>\n", "      <td>4</td>\n", "      <td>7</td>\n", "      <td>10203</td>\n", "      <td>102</td>\n", "      <td>6</td>\n", "      <td>True</td>\n", "      <td>104.0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>393219</td>\n", "      <td>79388</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>284492</td>\n", "      <td>1233</td>\n", "      <td>1783</td>\n", "      <td>78</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>11012</td>\n", "      <td>110</td>\n", "      <td>6</td>\n", "      <td>True</td>\n", "      <td>88.0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>393219</td>\n", "      <td>80831</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>25890</td>\n", "      <td>108</td>\n", "      <td>68</td>\n", "      <td>181</td>\n", "      <td>3</td>\n", "      <td>3</td>\n", "      <td>11202</td>\n", "      <td>112</td>\n", "      <td>2</td>\n", "      <td>True</td>\n", "      <td>134.0</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   user_id  item_id  click  gender  age  click_count  like_count  \\\n", "0   393219    73342      1       2    2           73           1   \n", "1   393219    17695      1       2    2        29552         209   \n", "2   393219    40457      1       2    2        20285         242   \n", "3   393219    79388      1       2    2       284492        1233   \n", "4   393219    80831      1       2    2        25890         108   \n", "\n", "   comment_count  read_percentage  item_score1  item_score2  category_second  \\\n", "0              0               25            3            3            12000   \n", "1             84               43            3            3            11204   \n", "2             90               43            4            7            10203   \n", "3           1783               78            3            3            11012   \n", "4             68              181            3            3            11202   \n", "\n", "   category_first  item_score3  read  read_time  share   like  follow  \\\n", "0             120            1  True        4.0  False  False   False   \n", "1             112            2  True       19.0  False  False   False   \n", "2             102            6  True      104.0  False  False   False   \n", "3             110            6  True       88.0  False  False   False   \n", "4             112            2  True      134.0  False  False   False   \n", "\n", "   favorite  \n", "0     False  \n", "1     False  \n", "2     False  \n", "3     False  \n", "4     False  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"collapsed": false}, "outputs": [], "source": ["for col in ['share', 'like', 'follow', 'favorite']:\n", "    data[col] = data[col].astype('int')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"collapsed": false}, "outputs": [], "source": ["for col in ['click_count', 'like_count', 'comment_count', 'read_percentage']:\n", "    data[col] = np.log(data[col].values + 1)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["(array([7.339000e+03, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 1.000000e+01,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 9.000000e+01, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 1.014453e+06, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        1.137000e+03, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 3.942000e+03,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 7.345000e+03, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 8.453000e+03, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        3.542000e+03, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 0.000000e+00,\n", "        0.000000e+00, 0.000000e+00, 0.000000e+00, 2.264000e+03]),\n", " array([0.  , 0.09, 0.18, 0.27, 0.36, 0.45, 0.54, 0.63, 0.72, 0.81, 0.9 ,\n", "        0.99, 1.08, 1.17, 1.26, 1.35, 1.44, 1.53, 1.62, 1.71, 1.8 , 1.89,\n", "        1.98, 2.07, 2.16, 2.25, 2.34, 2.43, 2.52, 2.61, 2.7 , 2.79, 2.88,\n", "        2.97, 3.06, 3.15, 3.24, 3.33, 3.42, 3.51, 3.6 , 3.69, 3.78, 3.87,\n", "        3.96, 4.05, 4.14, 4.23, 4.32, 4.41, 4.5 , 4.59, 4.68, 4.77, 4.86,\n", "        4.95, 5.04, 5.13, 5.22, 5.31, 5.4 , 5.49, 5.58, 5.67, 5.76, 5.85,\n", "        5.94, 6.03, 6.12, 6.21, 6.3 , 6.39, 6.48, 6.57, 6.66, 6.75, 6.84,\n", "        6.93, 7.02, 7.11, 7.2 , 7.29, 7.38, 7.47, 7.56, 7.65, 7.74, 7.83,\n", "        7.92, 8.01, 8.1 , 8.19, 8.28, 8.37, 8.46, 8.55, 8.64, 8.73, 8.82,\n", "        8.91, 9.  ]),\n", " <BarContainer object of 100 artists>)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# import matplotlib.pyplot as plt\n", "# plt.hist(data.item_score3, bins = 100)\n", "import matplotlib.pyplot as plt\n", "plt.hist(data.item_score2, bins = 100)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["9.0"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["  import pandas as pd\n", "\n", "# data = pd.read_csv(r\"C:\\Users\\<USER>\\Desktop\\Tenrec\\cold_data_0.3.csv\")\n", "\n", "data['item_score3'].quantile(0.999)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["user_id\n", "item_id\n", "click\n", "gender\n", "age\n", "click_count\n", "like_count\n", "comment_count\n", "read_percentage\n", "item_score1\n", "item_score2\n", "category_second\n", "category_first\n", "item_score3\n", "read\n", "read_time\n", "share\n", "like\n", "follow\n", "favorite\n"]}], "source": ["for col in data.columns:\n", "    print(col)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"collapsed": false}, "outputs": [], "source": ["data['item_score3'] = data['item_score3'].clip(upper = 10)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"collapsed": false}, "outputs": [], "source": ["scale_cols = [\n", "    'click_count',\n", "    'like_count',\n", "    'comment_count',\n", "    'read_percentage',\n", "    'item_score1',\n", "    'item_score2',\n", "    'item_score3'\n", "]\n", "scale_data = data[scale_cols].values\n", "from sklearn.preprocessing import MinMaxScaler\n", "\n", "scaler = MinMaxScaler(feature_range=(-1, 1))\n", "scaler.fit(scale_data)\n", "rolling_dict = (scale_cols, scaler)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"collapsed": false}, "outputs": [], "source": ["data[scale_cols] = scaler.transform(scale_data)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_id</th>\n", "      <th>item_id</th>\n", "      <th>click</th>\n", "      <th>gender</th>\n", "      <th>age</th>\n", "      <th>click_count</th>\n", "      <th>like_count</th>\n", "      <th>comment_count</th>\n", "      <th>read_percentage</th>\n", "      <th>item_score1</th>\n", "      <th>item_score2</th>\n", "      <th>category_second</th>\n", "      <th>category_first</th>\n", "      <th>item_score3</th>\n", "      <th>read_time</th>\n", "      <th>share</th>\n", "      <th>like</th>\n", "      <th>follow</th>\n", "      <th>favorite</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1048575.0</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "      <td>1.048575e+06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>6.017558e+05</td>\n", "      <td>1.100806e+05</td>\n", "      <td>1.0</td>\n", "      <td>1.318270e+00</td>\n", "      <td>2.674438e+00</td>\n", "      <td>3.392908e-01</td>\n", "      <td>-6.291423e-02</td>\n", "      <td>-2.417825e-02</td>\n", "      <td>1.676460e-01</td>\n", "      <td>5.033798e-01</td>\n", "      <td>-3.176433e-01</td>\n", "      <td>1.139182e+04</td>\n", "      <td>1.142028e+02</td>\n", "      <td>-9.518442e-02</td>\n", "      <td>5.291009e+01</td>\n", "      <td>1.149274e-02</td>\n", "      <td>1.517297e-02</td>\n", "      <td>9.670267e-04</td>\n", "      <td>5.577093e-03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>3.272428e+05</td>\n", "      <td>7.086640e+04</td>\n", "      <td>0.0</td>\n", "      <td>4.706243e-01</td>\n", "      <td>1.273393e+00</td>\n", "      <td>2.404084e-01</td>\n", "      <td>3.630162e-01</td>\n", "      <td>3.765873e-01</td>\n", "      <td>2.317931e-01</td>\n", "      <td>3.863170e-01</td>\n", "      <td>1.454587e-01</td>\n", "      <td>1.578161e+03</td>\n", "      <td>1.485303e+01</td>\n", "      <td>4.256981e-01</td>\n", "      <td>7.584976e+01</td>\n", "      <td>1.065864e-01</td>\n", "      <td>1.222406e-01</td>\n", "      <td>3.108203e-02</td>\n", "      <td>7.447143e-02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>1.100000e+01</td>\n", "      <td>1.480000e+02</td>\n", "      <td>1.0</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>-1.000000e+00</td>\n", "      <td>-1.000000e+00</td>\n", "      <td>-1.000000e+00</td>\n", "      <td>-1.000000e+00</td>\n", "      <td>-1.000000e+00</td>\n", "      <td>-1.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>-1.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>2.936360e+05</td>\n", "      <td>2.618300e+04</td>\n", "      <td>1.0</td>\n", "      <td>1.000000e+00</td>\n", "      <td>2.000000e+00</td>\n", "      <td>2.055175e-01</td>\n", "      <td>-2.784091e-01</td>\n", "      <td>-2.463583e-01</td>\n", "      <td>4.764850e-02</td>\n", "      <td>5.000000e-01</td>\n", "      <td>-3.333333e-01</td>\n", "      <td>1.080200e+04</td>\n", "      <td>1.080000e+02</td>\n", "      <td>-4.000000e-01</td>\n", "      <td>7.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>6.902920e+05</td>\n", "      <td>1.382030e+05</td>\n", "      <td>1.0</td>\n", "      <td>1.000000e+00</td>\n", "      <td>2.000000e+00</td>\n", "      <td>3.659032e-01</td>\n", "      <td>-4.542671e-02</td>\n", "      <td>3.144703e-02</td>\n", "      <td>2.171096e-01</td>\n", "      <td>5.000000e-01</td>\n", "      <td>-3.333333e-01</td>\n", "      <td>1.120200e+04</td>\n", "      <td>1.120000e+02</td>\n", "      <td>0.000000e+00</td>\n", "      <td>2.600000e+01</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>9.253360e+05</td>\n", "      <td>1.674855e+05</td>\n", "      <td>1.0</td>\n", "      <td>2.000000e+00</td>\n", "      <td>3.000000e+00</td>\n", "      <td>5.005657e-01</td>\n", "      <td>1.651667e-01</td>\n", "      <td>2.515496e-01</td>\n", "      <td>3.266175e-01</td>\n", "      <td>1.000000e+00</td>\n", "      <td>-3.333333e-01</td>\n", "      <td>1.210800e+04</td>\n", "      <td>1.210000e+02</td>\n", "      <td>2.000000e-01</td>\n", "      <td>7.000000e+01</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "      <td>0.000000e+00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>9.988720e+05</td>\n", "      <td>2.272830e+05</td>\n", "      <td>1.0</td>\n", "      <td>2.000000e+00</td>\n", "      <td>7.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.410200e+04</td>\n", "      <td>1.410000e+02</td>\n", "      <td>1.000000e+00</td>\n", "      <td>4.446000e+03</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "      <td>1.000000e+00</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["            user_id       item_id      click        gender           age  \\\n", "count  1.048575e+06  1.048575e+06  1048575.0  1.048575e+06  1.048575e+06   \n", "mean   6.017558e+05  1.100806e+05        1.0  1.318270e+00  2.674438e+00   \n", "std    3.272428e+05  7.086640e+04        0.0  4.706243e-01  1.273393e+00   \n", "min    1.100000e+01  1.480000e+02        1.0  0.000000e+00  0.000000e+00   \n", "25%    2.936360e+05  2.618300e+04        1.0  1.000000e+00  2.000000e+00   \n", "50%    6.902920e+05  1.382030e+05        1.0  1.000000e+00  2.000000e+00   \n", "75%    9.253360e+05  1.674855e+05        1.0  2.000000e+00  3.000000e+00   \n", "max    9.988720e+05  2.272830e+05        1.0  2.000000e+00  7.000000e+00   \n", "\n", "        click_count    like_count  comment_count  read_percentage  \\\n", "count  1.048575e+06  1.048575e+06   1.048575e+06     1.048575e+06   \n", "mean   3.392908e-01 -6.291423e-02  -2.417825e-02     1.676460e-01   \n", "std    2.404084e-01  3.630162e-01   3.765873e-01     2.317931e-01   \n", "min   -1.000000e+00 -1.000000e+00  -1.000000e+00    -1.000000e+00   \n", "25%    2.055175e-01 -2.784091e-01  -2.463583e-01     4.764850e-02   \n", "50%    3.659032e-01 -4.542671e-02   3.144703e-02     2.171096e-01   \n", "75%    5.005657e-01  1.651667e-01   2.515496e-01     3.266175e-01   \n", "max    1.000000e+00  1.000000e+00   1.000000e+00     1.000000e+00   \n", "\n", "        item_score1   item_score2  category_second  category_first  \\\n", "count  1.048575e+06  1.048575e+06     1.048575e+06    1.048575e+06   \n", "mean   5.033798e-01 -3.176433e-01     1.139182e+04    1.142028e+02   \n", "std    3.863170e-01  1.454587e-01     1.578161e+03    1.485303e+01   \n", "min   -1.000000e+00 -1.000000e+00     0.000000e+00    0.000000e+00   \n", "25%    5.000000e-01 -3.333333e-01     1.080200e+04    1.080000e+02   \n", "50%    5.000000e-01 -3.333333e-01     1.120200e+04    1.120000e+02   \n", "75%    1.000000e+00 -3.333333e-01     1.210800e+04    1.210000e+02   \n", "max    1.000000e+00  1.000000e+00     1.410200e+04    1.410000e+02   \n", "\n", "        item_score3     read_time         share          like        follow  \\\n", "count  1.048575e+06  1.048575e+06  1.048575e+06  1.048575e+06  1.048575e+06   \n", "mean  -9.518442e-02  5.291009e+01  1.149274e-02  1.517297e-02  9.670267e-04   \n", "std    4.256981e-01  7.584976e+01  1.065864e-01  1.222406e-01  3.108203e-02   \n", "min   -1.000000e+00  0.000000e+00  0.000000e+00  0.000000e+00  0.000000e+00   \n", "25%   -4.000000e-01  7.000000e+00  0.000000e+00  0.000000e+00  0.000000e+00   \n", "50%    0.000000e+00  2.600000e+01  0.000000e+00  0.000000e+00  0.000000e+00   \n", "75%    2.000000e-01  7.000000e+01  0.000000e+00  0.000000e+00  0.000000e+00   \n", "max    1.000000e+00  4.446000e+03  1.000000e+00  1.000000e+00  1.000000e+00   \n", "\n", "           favorite  \n", "count  1.048575e+06  \n", "mean   5.577093e-03  \n", "std    7.447143e-02  \n", "min    0.000000e+00  \n", "25%    0.000000e+00  \n", "50%    0.000000e+00  \n", "75%    0.000000e+00  \n", "max    1.000000e+00  "]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["data.describe()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"collapsed": false}, "outputs": [], "source": ["# data.to_csv(r'C:\\Users\\<USER>\\Desktop\\Tenrec\\scale_data.csv', index=False)\n", "data.to_csv('scale_data.csv', index=False)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"collapsed": false}, "outputs": [], "source": ["np.random.seed(1423)\n", "indices = np.random.uniform(size=(data.shape[0],))\n", "train = data.loc[(indices <= 0.8)].copy()\n", "valid = data.loc[(indices > 0.8)].copy()\n", "train.to_csv('train.csv', index = False)\n", "valid.to_csv('valid.csv', index = False)"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["227283"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["data.item_id.max()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"collapsed": false}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'result.csv'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mFileNotFoundError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/Users/<USER>/Desktop/taylor/homework/MMOE/recsys.ipynb 单元格 17\u001b[0m line \u001b[0;36m1\n\u001b[0;32m----> <a href='vscode-notebook-cell:/Users/<USER>/Desktop/taylor/homework/MMOE/recsys.ipynb#X22sZmlsZQ%3D%3D?line=0'>1</a>\u001b[0m res \u001b[39m=\u001b[39m pd\u001b[39m.\u001b[39;49mread_csv(\u001b[39m'\u001b[39;49m\u001b[39mresult.csv\u001b[39;49m\u001b[39m'\u001b[39;49m, encoding\u001b[39m=\u001b[39;49m\u001b[39m'\u001b[39;49m\u001b[39mISO-8859-1\u001b[39;49m\u001b[39m'\u001b[39;49m)\n", "File \u001b[0;32m~/anaconda3/envs/jupyer/lib/python3.8/site-packages/pandas/io/parsers/readers.py:912\u001b[0m, in \u001b[0;36mread_csv\u001b[0;34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[0m\n\u001b[1;32m    899\u001b[0m kwds_defaults \u001b[39m=\u001b[39m _refine_defaults_read(\n\u001b[1;32m    900\u001b[0m     dialect,\n\u001b[1;32m    901\u001b[0m     delimiter,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    908\u001b[0m     dtype_backend\u001b[39m=\u001b[39mdtype_backend,\n\u001b[1;32m    909\u001b[0m )\n\u001b[1;32m    910\u001b[0m kwds\u001b[39m.\u001b[39mupdate(kwds_defaults)\n\u001b[0;32m--> 912\u001b[0m \u001b[39mreturn\u001b[39;00m _read(filepath_or_buffer, kwds)\n", "File \u001b[0;32m~/anaconda3/envs/jupyer/lib/python3.8/site-packages/pandas/io/parsers/readers.py:577\u001b[0m, in \u001b[0;36m_read\u001b[0;34m(filepath_or_buffer, kwds)\u001b[0m\n\u001b[1;32m    574\u001b[0m _validate_names(kwds\u001b[39m.\u001b[39mget(\u001b[39m\"\u001b[39m\u001b[39mnames\u001b[39m\u001b[39m\"\u001b[39m, \u001b[39mNone\u001b[39;00m))\n\u001b[1;32m    576\u001b[0m \u001b[39m# Create the parser.\u001b[39;00m\n\u001b[0;32m--> 577\u001b[0m parser \u001b[39m=\u001b[39m TextFileReader(filepath_or_buffer, \u001b[39m*\u001b[39;49m\u001b[39m*\u001b[39;49mkwds)\n\u001b[1;32m    579\u001b[0m \u001b[39mif\u001b[39;00m chunksize \u001b[39mor\u001b[39;00m iterator:\n\u001b[1;32m    580\u001b[0m     \u001b[39mreturn\u001b[39;00m parser\n", "File \u001b[0;32m~/anaconda3/envs/jupyer/lib/python3.8/site-packages/pandas/io/parsers/readers.py:1407\u001b[0m, in \u001b[0;36mTextFileReader.__init__\u001b[0;34m(self, f, engine, **kwds)\u001b[0m\n\u001b[1;32m   1404\u001b[0m     \u001b[39mself\u001b[39m\u001b[39m.\u001b[39moptions[\u001b[39m\"\u001b[39m\u001b[39mhas_index_names\u001b[39m\u001b[39m\"\u001b[39m] \u001b[39m=\u001b[39m kwds[\u001b[39m\"\u001b[39m\u001b[39mhas_index_names\u001b[39m\u001b[39m\"\u001b[39m]\n\u001b[1;32m   1406\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mhandles: IOHandles \u001b[39m|\u001b[39m \u001b[39mNone\u001b[39;00m \u001b[39m=\u001b[39m \u001b[39mNone\u001b[39;00m\n\u001b[0;32m-> 1407\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39m_engine \u001b[39m=\u001b[39m \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49m_make_engine(f, \u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49mengine)\n", "File \u001b[0;32m~/anaconda3/envs/jupyer/lib/python3.8/site-packages/pandas/io/parsers/readers.py:1661\u001b[0m, in \u001b[0;36mTextFileReader._make_engine\u001b[0;34m(self, f, engine)\u001b[0m\n\u001b[1;32m   1659\u001b[0m     \u001b[39mif\u001b[39;00m \u001b[39m\"\u001b[39m\u001b[39mb\u001b[39m\u001b[39m\"\u001b[39m \u001b[39mnot\u001b[39;00m \u001b[39min\u001b[39;00m mode:\n\u001b[1;32m   1660\u001b[0m         mode \u001b[39m+\u001b[39m\u001b[39m=\u001b[39m \u001b[39m\"\u001b[39m\u001b[39mb\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[0;32m-> 1661\u001b[0m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mhandles \u001b[39m=\u001b[39m get_handle(\n\u001b[1;32m   1662\u001b[0m     f,\n\u001b[1;32m   1663\u001b[0m     mode,\n\u001b[1;32m   1664\u001b[0m     encoding\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49moptions\u001b[39m.\u001b[39;49mget(\u001b[39m\"\u001b[39;49m\u001b[39mencoding\u001b[39;49m\u001b[39m\"\u001b[39;49m, \u001b[39mNone\u001b[39;49;00m),\n\u001b[1;32m   1665\u001b[0m     compression\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49moptions\u001b[39m.\u001b[39;49mget(\u001b[39m\"\u001b[39;49m\u001b[39mcompression\u001b[39;49m\u001b[39m\"\u001b[39;49m, \u001b[39mNone\u001b[39;49;00m),\n\u001b[1;32m   1666\u001b[0m     memory_map\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49moptions\u001b[39m.\u001b[39;49mget(\u001b[39m\"\u001b[39;49m\u001b[39mmemory_map\u001b[39;49m\u001b[39m\"\u001b[39;49m, \u001b[39mFalse\u001b[39;49;00m),\n\u001b[1;32m   1667\u001b[0m     is_text\u001b[39m=\u001b[39;49mis_text,\n\u001b[1;32m   1668\u001b[0m     errors\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49moptions\u001b[39m.\u001b[39;49mget(\u001b[39m\"\u001b[39;49m\u001b[39mencoding_errors\u001b[39;49m\u001b[39m\"\u001b[39;49m, \u001b[39m\"\u001b[39;49m\u001b[39mstrict\u001b[39;49m\u001b[39m\"\u001b[39;49m),\n\u001b[1;32m   1669\u001b[0m     storage_options\u001b[39m=\u001b[39;49m\u001b[39mself\u001b[39;49m\u001b[39m.\u001b[39;49moptions\u001b[39m.\u001b[39;49mget(\u001b[39m\"\u001b[39;49m\u001b[39mstorage_options\u001b[39;49m\u001b[39m\"\u001b[39;49m, \u001b[39mNone\u001b[39;49;00m),\n\u001b[1;32m   1670\u001b[0m )\n\u001b[1;32m   1671\u001b[0m \u001b[39massert\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mhandles \u001b[39mis\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mNone\u001b[39;00m\n\u001b[1;32m   1672\u001b[0m f \u001b[39m=\u001b[39m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mhandles\u001b[39m.\u001b[39mhandle\n", "File \u001b[0;32m~/anaconda3/envs/jupyer/lib/python3.8/site-packages/pandas/io/common.py:859\u001b[0m, in \u001b[0;36mget_handle\u001b[0;34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[0m\n\u001b[1;32m    854\u001b[0m \u001b[39melif\u001b[39;00m \u001b[39misinstance\u001b[39m(handle, \u001b[39mstr\u001b[39m):\n\u001b[1;32m    855\u001b[0m     \u001b[39m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[1;32m    856\u001b[0m     \u001b[39m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[1;32m    857\u001b[0m     \u001b[39mif\u001b[39;00m ioargs\u001b[39m.\u001b[39mencoding \u001b[39mand\u001b[39;00m \u001b[39m\"\u001b[39m\u001b[39mb\u001b[39m\u001b[39m\"\u001b[39m \u001b[39mnot\u001b[39;00m \u001b[39min\u001b[39;00m ioargs\u001b[39m.\u001b[39mmode:\n\u001b[1;32m    858\u001b[0m         \u001b[39m# Encoding\u001b[39;00m\n\u001b[0;32m--> 859\u001b[0m         handle \u001b[39m=\u001b[39m \u001b[39mopen\u001b[39;49m(\n\u001b[1;32m    860\u001b[0m             handle,\n\u001b[1;32m    861\u001b[0m             ioargs\u001b[39m.\u001b[39;49mmode,\n\u001b[1;32m    862\u001b[0m             encoding\u001b[39m=\u001b[39;49mioargs\u001b[39m.\u001b[39;49mencoding,\n\u001b[1;32m    863\u001b[0m             errors\u001b[39m=\u001b[39;49merrors,\n\u001b[1;32m    864\u001b[0m             newline\u001b[39m=\u001b[39;49m\u001b[39m\"\u001b[39;49m\u001b[39m\"\u001b[39;49m,\n\u001b[1;32m    865\u001b[0m         )\n\u001b[1;32m    866\u001b[0m     \u001b[39melse\u001b[39;00m:\n\u001b[1;32m    867\u001b[0m         \u001b[39m# Binary mode\u001b[39;00m\n\u001b[1;32m    868\u001b[0m         handle \u001b[39m=\u001b[39m \u001b[39mopen\u001b[39m(handle, ioargs\u001b[39m.\u001b[39mmode)\n", "\u001b[0;31mFileNotFoundError\u001b[0m: [Errno 2] No such file or directory: 'result.csv'"]}], "source": ["res = pd.read_csv('result.csv', encoding='ISO-8859-1')\n", "\n"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"collapsed": false}, "outputs": [{"ename": "NameError", "evalue": "name 'res' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[33], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m res[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpred_like_label\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m=\u001b[39m \u001b[43mres\u001b[49m\u001b[38;5;241m.\u001b[39mpred_like\u001b[38;5;241m.\u001b[39mapply(\u001b[38;5;28;01mlambda\u001b[39;00m x: \u001b[38;5;241m1\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m x \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0.6\u001b[39m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m \u001b[38;5;241m0\u001b[39m)\n", "\u001b[1;31mNameError\u001b[0m: name 'res' is not defined"]}], "source": ["res['pred_like_label'] = res.pred_like.apply(lambda x: 1 if x > 0.6 else 0)"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"collapsed": false}, "outputs": [{"ename": "NameError", "evalue": "name 'item_num' is not defined", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 1\u001b[0m\n\u001b[1;32m----> 1\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mItem num: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[43mitem_num\u001b[49m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m      2\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mGender num: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mgender_num\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAge num: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mage_num\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m\"\u001b[39m)\n", "\u001b[1;31mNameError\u001b[0m: name 'item_num' is not defined"]}], "source": ["\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"collapsed": false, "pycharm": {"is_executing": true}}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 33, "metadata": {"collapsed": false}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 0}