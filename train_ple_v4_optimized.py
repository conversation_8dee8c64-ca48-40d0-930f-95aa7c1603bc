#!/usr/bin/env python3
"""
优化的PLE_V4训练脚本，目标AUC > 0.93
"""
import os
import sys
import argparse
import numpy as np
import pandas as pd
import torch
from torch.utils.data import DataLoader
import random

# 导入模型和数据集
from ple_v4 import PLE_V4, RecsysDataset
from training_recsys import metrics_auc, print_epoch_log

def random_set():
    random.seed(930)
    torch.manual_seed(1010)
    np.random.seed(6563)

def train_ple_v4_optimized():
    """优化的PLE_V4训练函数"""
    
    # 设置参数
    args = argparse.Namespace()
    args.batch_size = 256  # 减小batch size以获得更好的梯度估计
    args.epoches = 50      # 增加训练轮数
    args.lr = 1e-4         # 更小的学习率
    args.num_workers = 8
    args.model_type = 'ple_v4'
    args.checkpoint = 'checkpoint/ple_v4_optimized_best.pth'
    
    # 创建checkpoint目录
    os.makedirs('checkpoint', exist_ok=True)
    
    # 设备设置
    args.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {args.device}")
    
    # 数据准备
    label_col = ["like", 'share']
    train = pd.read_csv("train.csv")
    valid = pd.read_csv("valid.csv")
   
    feature_cols = [
        'user_id', 'item_id', 'gender', 'age',
        'click_count', 'like_count', 'comment_count', 'read_percentage',
        'item_score1', 'item_score2', 'item_score3'
    ]

    X_train = train[feature_cols].values
    y_train = train[label_col].values
    X_eval = valid[feature_cols].values
    y_eval = valid[label_col].values

    # 数据集和数据加载器
    train_data = RecsysDataset(X_train, y_train)
    eval_data = RecsysDataset(X_eval, y_eval)
    
    train_dataloader = DataLoader(train_data, batch_size=args.batch_size, 
                                 num_workers=args.num_workers, shuffle=True)
    eval_dataloader = DataLoader(eval_data, batch_size=args.batch_size, 
                                num_workers=args.num_workers, shuffle=False)

    # 设置随机种子
    random_set()
    
    # 创建模型
    model = PLE_V4(
        item_num=230000, 
        gender_num=3, 
        age_num=8, 
        item_dim=7, 
        n_output=2, 
        embedding_dim=64,  # 使用更大的embedding
        device=args.device
    ).to(device=args.device)
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 损失函数和优化器
    criterion = torch.nn.BCELoss()
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=args.lr, 
        weight_decay=2e-3,  # 适中的权重衰减
        betas=(0.9, 0.999),
        eps=1e-8
    )
    
    # 学习率调度器
    scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(
        optimizer, T_0=10, T_mult=2, eta_min=1e-6
    )
    
    # 训练循环
    best_eval_loss = float('inf')
    best_auc = 0.0
    patience_counter = 0
    patience = 8  # 更大的耐心值
    
    print("开始训练...")
    print(f"目标: 验证集AUC > 0.93")
    print("-" * 60)
    
    for epoch in range(args.epoches):
        # 训练阶段
        model.train()
        epoch_loss_like, epoch_loss_share = [], []
        epoch_pred, epoch_labels = [], []
        
        for batch_idx, batch_data in enumerate(train_dataloader):
            optimizer.zero_grad()
            
            ((x_item_id, x_gender, x_age, x_item), y) = batch_data
            x_item_id = x_item_id.to(device=args.device)
            x_gender = x_gender.to(device=args.device)
            x_age = x_age.to(device=args.device)
            x_item = x_item.to(device=args.device)
            y = y.to(device=args.device)
            
            y_hat = model(x_item_id, x_gender, x_age, x_item)
            
            loss_like = criterion(y_hat[:, 0], y[:, 0])
            loss_share = criterion(y_hat[:, 1], y[:, 1])
            total_loss = loss_like + loss_share
            
            total_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            epoch_loss_like.append(loss_like.item())
            epoch_loss_share.append(loss_share.item())
            epoch_pred.append(y_hat.cpu().detach())
            epoch_labels.append(y.cpu().detach())
        
        # 学习率调度
        scheduler.step()
        
        # 计算训练AUC
        train_pred = torch.concat(epoch_pred, axis=0)
        train_labels = torch.concat(epoch_labels, axis=0)
        train_auc_like, train_auc_share = metrics_auc(train_pred, train_labels)
        train_avg_auc = (train_auc_like + train_auc_share) / 2
        
        print(f"Epoch {epoch+1}/{args.epoches}")
        print(f"训练 - Loss Like: {np.mean(epoch_loss_like):.4f}, Loss Share: {np.mean(epoch_loss_share):.4f}")
        print(f"训练 - AUC Like: {train_auc_like:.6f}, AUC Share: {train_auc_share:.6f}, 平均: {train_avg_auc:.6f}")
        
        # 验证阶段
        model.eval()
        val_loss_like, val_loss_share = [], []
        val_pred, val_labels = [], []
        
        with torch.no_grad():
            for batch_data in eval_dataloader:
                ((x_item_id, x_gender, x_age, x_item), y) = batch_data
                x_item_id = x_item_id.to(device=args.device)
                x_gender = x_gender.to(device=args.device)
                x_age = x_age.to(device=args.device)
                x_item = x_item.to(device=args.device)
                y = y.to(device=args.device)
                
                y_hat = model(x_item_id, x_gender, x_age, x_item)
                
                loss_like = criterion(y_hat[:, 0], y[:, 0])
                loss_share = criterion(y_hat[:, 1], y[:, 1])
                
                val_loss_like.append(loss_like.item())
                val_loss_share.append(loss_share.item())
                val_pred.append(y_hat.cpu())
                val_labels.append(y.cpu())
        
        # 计算验证AUC
        val_pred_tensor = torch.concat(val_pred, axis=0)
        val_labels_tensor = torch.concat(val_labels, axis=0)
        val_auc_like, val_auc_share = metrics_auc(val_pred_tensor, val_labels_tensor)
        val_avg_auc = (val_auc_like + val_auc_share) / 2
        val_total_loss = np.mean(val_loss_like) + np.mean(val_loss_share)
        
        print(f"验证 - Loss Like: {np.mean(val_loss_like):.4f}, Loss Share: {np.mean(val_loss_share):.4f}")
        print(f"验证 - AUC Like: {val_auc_like:.6f}, AUC Share: {val_auc_share:.6f}, 平均: {val_avg_auc:.6f}")
        print(f"当前学习率: {optimizer.param_groups[0]['lr']:.2e}")
        
        # 保存最佳模型
        if val_total_loss < best_eval_loss:
            best_eval_loss = val_total_loss
            patience_counter = 0
            torch.save(model.state_dict(), args.checkpoint)
            print(f"✓ 新的最佳模型已保存！验证损失: {best_eval_loss:.6f}")
        else:
            patience_counter += 1
            print(f"验证损失没有改善，耐心计数器: {patience_counter}/{patience}")
        
        if val_avg_auc > best_auc:
            best_auc = val_avg_auc
            print(f"✓ 新的最佳AUC: {best_auc:.6f}")
            
        # 检查是否达到目标
        if val_avg_auc >= 0.93:
            print(f"🎉 达到目标AUC {val_avg_auc:.6f} >= 0.93!")
            torch.save(model.state_dict(), args.checkpoint.replace('.pth', '_target_achieved.pth'))
            
        print("-" * 60)
        
        # 早停检查
        if patience_counter >= patience:
            print(f"早停触发！最佳AUC: {best_auc:.6f}")
            break
    
    print(f"训练完成！最佳验证AUC: {best_auc:.6f}")
    return best_auc

if __name__ == "__main__":
    best_auc = train_ple_v4_optimized()
    if best_auc >= 0.93:
        print("🎉 成功达到目标AUC!")
    else:
        print(f"❌ 未达到目标AUC，当前最佳: {best_auc:.6f}")
