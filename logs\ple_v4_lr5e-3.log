nohup: ignoring input
Namespace(batch_size=512, checkpoint='checkpoint/ple_v4_best.pth', device=device(type='cuda'), epoches=10, loss=BCELoss(), lr=0.005, model_type='ple_v4', num_workers=8, optimizer=<PERSON> (
Parameter Group 0
    amsgrad: False
    betas: (0.9, 0.999)
    eps: 1e-08
    lr: 0.005
    weight_decay: 0
))
/home/<USER>/anaconda3/envs/torch/lib/python3.6/site-packages/torchmetrics/utilities/prints.py:36: UserWarning: Metric `AUROC` will save all targets and predictions in buffer. For large datasets this may lead to large memory footprint.
  warnings.warn(*args, **kwargs)
Epoch: 0/10, epoch_loss_like: 0.0817, epoch_loss_share: 0.0782    auc_like: 0.796726, auc_share: 0.730839
Test Result: 
Epoch: 0/10, epoch_loss_like: 0.0634, epoch_loss_share: 0.0556    auc_like: 0.865446, auc_share: 0.815700


Epoch: 1/10, epoch_loss_like: 0.0622, epoch_loss_share: 0.0541    auc_like: 0.867724, auc_share: 0.825479
Test Result: 
Epoch: 1/10, epoch_loss_like: 0.0578, epoch_loss_share: 0.0507    auc_like: 0.907493, auc_share: 0.874186


Epoch: 2/10, epoch_loss_like: 0.0569, epoch_loss_share: 0.0495    auc_like: 0.909985, auc_share: 0.884557
Test Result: 
Epoch: 2/10, epoch_loss_like: 0.0514, epoch_loss_share: 0.0448    auc_like: 0.942011, auc_share: 0.926103


Epoch: 3/10, epoch_loss_like: 0.0515, epoch_loss_share: 0.0445    auc_like: 0.937755, auc_share: 0.923790
Test Result: 
Epoch: 3/10, epoch_loss_like: 0.0481, epoch_loss_share: 0.0399    auc_like: 0.954969, auc_share: 0.948435


Epoch: 4/10, epoch_loss_like: 0.0474, epoch_loss_share: 0.0404    auc_like: 0.951995, auc_share: 0.944711
Test Result: 
Epoch: 4/10, epoch_loss_like: 0.0424, epoch_loss_share: 0.0356    auc_like: 0.965634, auc_share: 0.963633


Epoch: 5/10, epoch_loss_like: 0.0439, epoch_loss_share: 0.0369    auc_like: 0.961409, auc_share: 0.958652
Test Result: 
Epoch: 5/10, epoch_loss_like: 0.0406, epoch_loss_share: 0.0325    auc_like: 0.971781, auc_share: 0.973119


Epoch: 6/10, epoch_loss_like: 0.0413, epoch_loss_share: 0.0340    auc_like: 0.967546, auc_share: 0.967578
Test Result: 
Epoch: 6/10, epoch_loss_like: 0.0401, epoch_loss_share: 0.0310    auc_like: 0.974621, auc_share: 0.976841


Epoch: 7/10, epoch_loss_like: 0.0394, epoch_loss_share: 0.0323    auc_like: 0.971641, auc_share: 0.972093
Test Result: 
Epoch: 7/10, epoch_loss_like: 0.0365, epoch_loss_share: 0.0311    auc_like: 0.978299, auc_share: 0.978175


Epoch: 8/10, epoch_loss_like: 0.0380, epoch_loss_share: 0.0309    auc_like: 0.974064, auc_share: 0.975401
Test Result: 
Epoch: 8/10, epoch_loss_like: 0.0352, epoch_loss_share: 0.0284    auc_like: 0.979360, auc_share: 0.980923


Epoch: 9/10, epoch_loss_like: 0.0372, epoch_loss_share: 0.0300    auc_like: 0.975441, auc_share: 0.977222
Test Result: 
Epoch: 9/10, epoch_loss_like: 0.0354, epoch_loss_share: 0.0278    auc_like: 0.979244, auc_share: 0.982188


