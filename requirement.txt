1.  mmoe的网络模型，在当前腾讯数据集上实现，使用当前的两个label，share和like，得到至少auc>0.6以上的结果；
    去网上找其他人的结果，对比作为baseline。如果这个数据集找不到baseline，去寻找其他能找到baseline的数据集，
    之后就使用这个数据集来作为使用的数据集。
2.  调整mmoe中gate和expert中的特征维度，一般情况下user_id/item_id embedding会作为通用的feature，
    某些表征user或item的特征的比如年龄、性别可作为expert或gate中。  done
3. mmoe的改进实验，改进点：
    a. 可以使用多层的expert(去参考ple怎么实现)，并且在每一层都加入不同的gate网络。
    b. 在expert内部，使用类似dcn/dcn-v2，或者attention/lstm等。 
    c. 替换gate的结构，可以改成attention输出weight，替换现在的softmax输出概率，即去计算gate和expert之间的attention score.  
    d. 可以考虑在expert和gate combine结果输出后（比如当前是128维），继续用对应gate的输出乘到mlp的输出层，即在128层后乘上gate的第一个概率输出
    （假设一共有2个概率输出，代表两个expert，这里强化第一个expert），128之后的mlp连接到了64层，再乘这个概率输出，64-32后再乘这个概率，
    这样能够更加强调gate的作用。
4. 实现后和基线对比，得到实验结果，注意这里的改进gate和expert里使用的特征不要再改变了。



base:       auc_like: 0.931764, auc_share: 0.934108
PLE_pure:   auc_like: 0.942273, auc_share: 0.929905
PLE_v2:     auc_like: 0.974267, auc_share: 0.963796
PLE_v3:     auc_like: 0.974408, auc_share: 0.974660
PLE_v4:     auc_like: 0.979244, auc_share: 0.982188


PLE_v2:修改了特征的embeding_dim以及dense特征的处理
PLE_v3: 修改了gate的聚合方式，每层embedding以更充分的交互方式
PLE_v4: 在v3的基础上，在expert内部增加了FM模块